"use client"

import type React from "react"

import { useState } from "react"
import {
  Camera,
  Leaf,
  Heart,
  User,
  Home,
  Plus,
  ArrowRight,
  AlertTriangle,
  Droplets,
  Sun,
  Thermometer,
  CheckCircle,
  XCircle,
  Upload,
  Loader2,
  Bell,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import Image from "next/image"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

type Screen = "home" | "camera" | "results" | "collection" | "profile" | "diagnose"

type PlantInCollection = {
  id: number
  name: string
  nickname: string
  health: number
  lastWateredDate: Date
  image: string
  status: "healthy" | "needs-attention"
  wateringFrequencyDays?: number
  notes?: string
}

export default function FloraVisionApp() {
  // Utility function to calculate days ago
  const getDaysAgo = (date: Date): string => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const plantDate = new Date(date)
    plantDate.setHours(0, 0, 0, 0)

    const diffTime = today.getTime() - plantDate.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Today"
    if (diffDays === 1) return "Yesterday"
    return `${diffDays} days ago`
  }

  const [currentScreen, setCurrentScreen] = useState<Screen>("home")
  const [selectedPlant, setSelectedPlant] = useState<any>(null)

  const [myPlants, setMyPlants] = useState<PlantInCollection[]>([
    {
      id: 1,
      name: "Monstera Deliciosa",
      nickname: "My Monstera",
      health: 95,
      lastWateredDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=80&width=80",
      status: "healthy",
      wateringFrequencyDays: 7,
      notes: "Loves bright indirect light.",
    },
    {
      id: 2,
      name: "Snake Plant",
      nickname: "Office Snake",
      health: 88,
      lastWateredDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=80&width=80",
      status: "healthy",
      wateringFrequencyDays: 14,
      notes: "Very low maintenance.",
    },
    {
      id: 3,
      name: "Fiddle Leaf Fig",
      nickname: "Fiddle",
      health: 72,
      lastWateredDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=80&width=80",
      status: "needs-attention",
      wateringFrequencyDays: 5,
      notes: "Needs consistent watering, prone to brown spots.",
    },
  ])
  const [nextPlantId, setNextPlantId] = useState(myPlants.length + 1)
  const [showAddPlantDialog, setShowAddPlantDialog] = useState(false)

  const recentScans = [
    {
      id: 1,
      name: "Peace Lily",
      confidence: 98,
      date: "Today",
      image: "/placeholder.svg?height=60&width=60",
    },
    {
      id: 2,
      name: "Rubber Plant",
      confidence: 94,
      date: "Yesterday",
      image: "/placeholder.svg?height=60&width=60",
    },
  ]

  const handleAddPlant = (nickname: string, wateringFrequencyDays: number | undefined, notes: string) => {
    if (selectedPlant) {
      const newPlant: PlantInCollection = {
        id: nextPlantId,
        name: selectedPlant.name,
        nickname: nickname,
        health: 100,
        lastWateredDate: new Date(),
        image: selectedPlant.image,
        status: "healthy",
        wateringFrequencyDays: wateringFrequencyDays,
        notes,
      }
      setMyPlants((prevPlants) => [...prevPlants, newPlant])
      setNextPlantId((prevId) => prevId + 1)
      setShowAddPlantDialog(false)
      setCurrentScreen("collection")
    }
  }

  const HomeScreen = () => (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Enhanced Header with Status Bar */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-100/50 px-6 pt-14 pb-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Leaf className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 tracking-tight">FloraVision</h1>
              <p className="text-emerald-600 text-sm font-medium">Your plant companion</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="icon" className="w-10 h-10 rounded-full bg-gray-100/50">
              <Bell className="h-5 w-5 text-gray-600" />
            </Button>
            <Avatar className="h-10 w-10 ring-2 ring-emerald-100">
              <AvatarImage src="/placeholder.svg?height=40&width=40" />
              <AvatarFallback className="bg-emerald-100 text-emerald-700 font-semibold">JD</AvatarFallback>
            </Avatar>
          </div>
        </div>

        {/* Enhanced Quick Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={() => setCurrentScreen("camera")}
            className="h-20 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-3xl flex flex-col items-center justify-center gap-2 shadow-lg shadow-emerald-500/25 transition-all duration-200 active:scale-95"
          >
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <Camera className="h-5 w-5" />
            </div>
            <span className="text-sm font-semibold">Identify Plant</span>
          </Button>
          <Button
            onClick={() => setCurrentScreen("diagnose")}
            variant="outline"
            className="h-20 border-2 border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 rounded-3xl flex flex-col items-center justify-center gap-2 hover:bg-gradient-to-r hover:from-orange-100 hover:to-amber-100 transition-all duration-200 active:scale-95"
          >
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5" />
            </div>
            <span className="text-sm font-semibold">Diagnose Issue</span>
          </Button>
        </div>
      </div>

      {/* Enhanced My Plants Section */}
      <div className="px-6 py-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">My Plants</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentScreen("collection")}
            className="text-emerald-600 font-medium hover:bg-emerald-50 rounded-full px-4"
          >
            View All <ArrowRight className="h-4 w-4 ml-1" />
          </Button>
        </div>

        <div className="space-y-4">
          {myPlants.slice(0, 2).map((plant) => (
            <Card
              key={plant.id}
              className="border-0 shadow-lg shadow-gray-200/50 bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden transition-all duration-200 hover:shadow-xl hover:shadow-gray-200/60"
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Image
                      src={plant.image || "/placeholder.svg"}
                      alt={plant.name}
                      width={70}
                      height={70}
                      className="rounded-2xl object-cover shadow-md"
                    />
                    <div
                      className={`absolute -top-1 -right-1 w-4 h-4 rounded-full ${plant.status === "healthy" ? "bg-emerald-500" : "bg-orange-500"} border-2 border-white`}
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-900 text-lg">{plant.nickname}</h3>
                    <p className="text-gray-600 text-sm mb-3">{plant.name}</p>
                    <div className="flex items-center space-x-3">
                      <div className="flex-1">
                        <Progress
                          value={plant.health}
                          className="h-2 bg-gray-100"
                          style={{
                            background: "linear-gradient(to right, #f3f4f6, #f3f4f6)",
                          }}
                        />
                      </div>
                      <span className="text-sm font-semibold text-gray-700">{plant.health}%</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge
                      variant={plant.status === "healthy" ? "default" : "secondary"}
                      className={`mb-2 font-medium ${
                        plant.status === "healthy"
                          ? "bg-emerald-100 text-emerald-800 hover:bg-emerald-100"
                          : "bg-orange-100 text-orange-800 hover:bg-orange-100"
                      }`}
                    >
                      {plant.status === "healthy" ? "Healthy" : "Needs Care"}
                    </Badge>
                    <p className="text-xs text-gray-500">Watered {getDaysAgo(plant.lastWateredDate)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Enhanced Recent Scans */}
      <div className="px-6 pb-24">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Recent Scans</h2>
        <div className="space-y-3">
          {recentScans.map((scan) => (
            <Card
              key={scan.id}
              className="border-0 shadow-md shadow-gray-200/40 bg-white/60 backdrop-blur-sm rounded-2xl overflow-hidden transition-all duration-200 hover:shadow-lg hover:shadow-gray-200/50"
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  <Image
                    src={scan.image || "/placeholder.svg"}
                    alt={scan.name}
                    width={50}
                    height={50}
                    className="rounded-xl object-cover shadow-sm"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{scan.name}</h3>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium text-emerald-600">{scan.confidence}%</span> confidence • {scan.date}
                    </p>
                  </div>
                  <ArrowRight className="h-5 w-5 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )

  const CameraScreen = () => (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Camera Viewfinder with Gradient Overlay */}
      <div className="absolute inset-0">
        <Image
          src="/placeholder.svg?height=800&width=400"
          alt="Camera viewfinder"
          width={400}
          height={800}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/40" />
      </div>

      {/* Enhanced Header */}
      <div className="absolute top-0 left-0 right-0 pt-14 px-6 z-10">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentScreen("home")}
            className="text-white hover:bg-white/20 rounded-full px-4 py-2 backdrop-blur-sm bg-black/20"
          >
            Cancel
          </Button>
          <h1 className="text-white font-semibold text-lg">Plant Identifier</h1>
          <div className="w-16" />
        </div>
      </div>

      {/* Enhanced Scanning Frame */}
      <div className="absolute inset-0 flex items-center justify-center z-10">
        <div className="w-80 h-80 relative">
          {/* Animated scanning border */}
          <div className="absolute inset-0 border-4 border-white/30 rounded-3xl" />
          <div className="absolute top-0 left-0 w-12 h-12 border-t-4 border-l-4 border-emerald-400 rounded-tl-3xl animate-pulse" />
          <div className="absolute top-0 right-0 w-12 h-12 border-t-4 border-r-4 border-emerald-400 rounded-tr-3xl animate-pulse" />
          <div className="absolute bottom-0 left-0 w-12 h-12 border-b-4 border-l-4 border-emerald-400 rounded-bl-3xl animate-pulse" />
          <div className="absolute bottom-0 right-0 w-12 h-12 border-b-4 border-r-4 border-emerald-400 rounded-br-3xl animate-pulse" />

          {/* Scanning line animation */}
          <div className="absolute inset-4 overflow-hidden rounded-2xl">
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-emerald-400 to-transparent animate-pulse" />
          </div>
        </div>
      </div>

      {/* Enhanced Instructions */}
      <div className="absolute bottom-40 left-0 right-0 px-6 z-10">
        <div className="bg-black/70 backdrop-blur-xl rounded-3xl p-6 text-center border border-white/10">
          <p className="text-white text-base font-medium mb-2">Position the plant within the frame</p>
          <p className="text-white/80 text-sm">Make sure the plant is well-lit and clearly visible</p>
        </div>
      </div>

      {/* Enhanced Capture Button */}
      <div className="absolute bottom-12 left-0 right-0 flex justify-center z-10">
        <Button
          onClick={() => {
            setSelectedPlant({
              name: "Monstera Deliciosa",
              scientificName: "Monstera deliciosa",
              confidence: 96,
              family: "Araceae",
              commonNames: ["Swiss Cheese Plant", "Split-leaf Philodendron"],
              image: "/placeholder.svg?height=300&width=300",
            })
            setCurrentScreen("results")
          }}
          className="w-24 h-24 rounded-full bg-white hover:bg-gray-100 border-8 border-white/30 shadow-2xl transition-all duration-200 active:scale-95"
        >
          <div className="w-20 h-20 rounded-full bg-white shadow-inner" />
        </Button>
      </div>
    </div>
  )

  const ResultsScreen = () => (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Enhanced Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-100/50 px-6 pt-14 pb-6">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentScreen("home")}
            className="text-gray-600 hover:bg-gray-100 rounded-full px-4"
          >
            ← Back
          </Button>
          <h1 className="text-lg font-bold text-gray-900">Plant Identified</h1>
          <Button variant="ghost" size="icon" className="text-rose-500 hover:bg-rose-50 rounded-full">
            <Heart className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Enhanced Plant Image */}
      <div className="px-6 py-6">
        <div className="relative rounded-3xl overflow-hidden bg-white shadow-2xl shadow-gray-200/50">
          <Image
            src={selectedPlant?.image || "/placeholder.svg?height=300&width=400&query=monstera+deliciosa+plant"}
            alt={selectedPlant?.name || "Plant"}
            width={400}
            height={300}
            className="w-full h-72 object-cover"
          />
          <div className="absolute top-6 right-6">
            <Badge className="bg-emerald-600 text-white font-semibold px-4 py-2 rounded-full shadow-lg">
              {selectedPlant?.confidence || 96}% Match
            </Badge>
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
        </div>
      </div>

      {/* Enhanced Plant Info */}
      <div className="px-6 space-y-6 pb-24">
        <Card className="border-0 shadow-xl shadow-gray-200/50 bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden">
          <CardContent className="p-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">{selectedPlant?.name || "Monstera Deliciosa"}</h2>
            <p className="text-gray-600 italic text-lg mb-6">{selectedPlant?.scientificName || "Monstera deliciosa"}</p>
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600 font-medium">Family:</span>
                <span className="font-semibold text-gray-900">{selectedPlant?.family || "Araceae"}</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-600 font-medium">Common Names:</span>
                <span className="font-semibold text-gray-900 text-right">Swiss Cheese Plant</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Care Requirements */}
        <Card className="border-0 shadow-xl shadow-gray-200/50 bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-900">Care Requirements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-2xl">
              <div className="w-14 h-14 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Sun className="h-7 w-7 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-bold text-gray-900 text-lg">Light</h3>
                <p className="text-gray-700">Bright, indirect sunlight</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Droplets className="h-7 w-7 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-bold text-gray-900 text-lg">Water</h3>
                <p className="text-gray-700">Weekly, when soil is dry</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-red-50 to-rose-50 rounded-2xl">
              <div className="w-14 h-14 bg-gradient-to-br from-red-400 to-rose-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Thermometer className="h-7 w-7 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-bold text-gray-900 text-lg">Temperature</h3>
                <p className="text-gray-700">65-80°F (18-27°C)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={() => setShowAddPlantDialog(true)}
            className="h-14 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-2xl font-semibold shadow-lg shadow-emerald-500/25 transition-all duration-200 active:scale-95"
          >
            <Plus className="h-5 w-5 mr-2" />
            Add to My Plants
          </Button>
          <Button
            variant="outline"
            className="h-14 border-2 border-gray-200 bg-white/80 backdrop-blur-sm rounded-2xl font-semibold hover:bg-gray-50 transition-all duration-200 active:scale-95"
          >
            Share Result
          </Button>
        </div>
      </div>

      {showAddPlantDialog && (
        <AddPlantDialog
          selectedPlant={selectedPlant}
          onClose={() => setShowAddPlantDialog(false)}
          onSave={handleAddPlant}
        />
      )}
    </div>
  )

  const CollectionScreen = () => (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Enhanced Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-100/50 px-6 pt-14 pb-6">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentScreen("home")}
            className="text-gray-600 hover:bg-gray-100 rounded-full px-4"
          >
            ← Back
          </Button>
          <h1 className="text-lg font-bold text-gray-900">My Plants</h1>
          <Button variant="ghost" size="icon" className="text-emerald-600 hover:bg-emerald-50 rounded-full">
            <Plus className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Enhanced Plants Grid */}
      <div className="px-6 py-8 space-y-6 pb-24">
        {myPlants.map((plant) => (
          <Card
            key={plant.id}
            className="border-0 shadow-xl shadow-gray-200/50 bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden transition-all duration-200 hover:shadow-2xl hover:shadow-gray-200/60"
          >
            <CardContent className="p-6">
              <div className="flex items-center space-x-5">
                <div className="relative">
                  <Image
                    src={plant.image || "/placeholder.svg"}
                    alt={plant.name}
                    width={90}
                    height={90}
                    className="rounded-3xl object-cover shadow-lg"
                  />
                  <div
                    className={`absolute -top-2 -right-2 w-6 h-6 rounded-full ${plant.status === "healthy" ? "bg-emerald-500" : "bg-orange-500"} border-3 border-white shadow-lg`}
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-gray-900 text-xl mb-1">{plant.nickname}</h3>
                  <p className="text-gray-600 mb-3">{plant.name}</p>
                  {plant.wateringFrequencyDays && (
                    <p className="text-sm text-gray-700 mb-2">
                      <span className="font-semibold">Watering:</span> Every {plant.wateringFrequencyDays} days
                    </p>
                  )}
                  {plant.notes && (
                    <p className="text-sm text-gray-700 line-clamp-1 mb-3">
                      <span className="font-semibold">Notes:</span> {plant.notes}
                    </p>
                  )}
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                    <span className="font-medium">Health: {plant.health}%</span>
                    <span>•</span>
                    <span>Watered {getDaysAgo(plant.lastWateredDate)}</span>
                  </div>
                  <Progress value={plant.health} className="h-3 bg-gray-100 rounded-full" />
                </div>
                <ArrowRight className="h-6 w-6 text-gray-400" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  interface AddPlantDialogProps {
    selectedPlant: any
    onClose: () => void
    onSave: (nickname: string, wateringFrequencyDays: number | undefined, notes: string) => void
  }

  const AddPlantDialog: React.FC<AddPlantDialogProps> = ({ selectedPlant, onClose, onSave }) => {
    const [nickname, setNickname] = useState(selectedPlant?.name || "")
    const [wateringFrequencyInput, setWateringFrequencyInput] = useState<string>("")
    const [notes, setNotes] = useState("")

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      const wateringFrequencyDays = wateringFrequencyInput ? Number.parseInt(wateringFrequencyInput, 10) : undefined
      onSave(nickname, wateringFrequencyDays, notes)
    }

    return (
      <Dialog open={true} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[425px] rounded-3xl border-0 shadow-2xl bg-white/95 backdrop-blur-xl">
          <DialogHeader className="pb-6">
            <DialogTitle className="text-2xl font-bold text-gray-900">Add {selectedPlant?.name}</DialogTitle>
            <DialogDescription className="text-gray-600">
              Customize care details for your new plant companion.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-6 py-4">
              <div className="space-y-2">
                <Label htmlFor="nickname" className="text-sm font-semibold text-gray-700">
                  Plant Nickname
                </Label>
                <Input
                  id="nickname"
                  value={nickname}
                  onChange={(e) => setNickname(e.target.value)}
                  className="h-12 rounded-2xl border-2 border-gray-200 focus:border-emerald-500 bg-white/80"
                  placeholder="Give your plant a name..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="watering" className="text-sm font-semibold text-gray-700">
                  Watering Schedule (Days)
                </Label>
                <Input
                  id="watering"
                  type="number"
                  placeholder="e.g., 7"
                  value={wateringFrequencyInput}
                  onChange={(e) => setWateringFrequencyInput(e.target.value)}
                  className="h-12 rounded-2xl border-2 border-gray-200 focus:border-emerald-500 bg-white/80"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="notes" className="text-sm font-semibold text-gray-700">
                  Care Notes
                </Label>
                <Textarea
                  id="notes"
                  placeholder="Any specific observations or care tips..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="min-h-[100px] rounded-2xl border-2 border-gray-200 focus:border-emerald-500 bg-white/80 resize-none"
                />
              </div>
            </div>
            <DialogFooter className="pt-6">
              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-2xl font-semibold shadow-lg shadow-emerald-500/25"
              >
                Save Plant
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  const DiagnoseScreen = () => {
    const commonSymptoms = [
      "Yellowing Leaves",
      "Wilting",
      "Brown Spots",
      "Pests Visible",
      "Stunted Growth",
      "Dropping Leaves",
      "Mushy Stem",
      "White Powder",
    ]
    const [selectedSymptoms, setSelectedSymptoms] = useState<string[]>([])
    const [photoFile, setPhotoFile] = useState<string | null>(null)
    const [remarks, setRemarks] = useState<string>("")
    const [diagnosisResult, setDiagnosisResult] = useState<any>(null)
    const [isLoadingDiagnosis, setIsLoadingDiagnosis] = useState(false)

    const toggleSymptom = (symptom: string) => {
      setSelectedSymptoms((prev) => (prev.includes(symptom) ? prev.filter((s) => s !== symptom) : [...prev, symptom]))
    }

    const handleTakePhoto = () => {
      setPhotoFile("/placeholder.svg?height=200&width=200")
    }

    const handleUploadPhoto = () => {
      setPhotoFile("/placeholder.svg?height=200&width=200")
    }

    const runDiagnosis = () => {
      setIsLoadingDiagnosis(true)
      setDiagnosisResult(null)

      setTimeout(() => {
        let issue = "No specific issue detected"
        let severity = "Low"
        let advice = "Your plant appears healthy based on provided information. Continue regular care."
        let icon = <CheckCircle className="h-8 w-8 text-emerald-500" />

        const combinedInput =
          (selectedSymptoms.length > 0 ? selectedSymptoms.join(", ") : "") +
          (remarks ? (selectedSymptoms.length > 0 ? ". " : "") + remarks : "") +
          (photoFile ? (selectedSymptoms.length > 0 || remarks ? ". " : "") + "Photo provided." : "")

        if (
          (selectedSymptoms.includes("Yellowing Leaves") && selectedSymptoms.includes("Wilting")) ||
          remarks.toLowerCase().includes("overwater") ||
          remarks.toLowerCase().includes("mushy")
        ) {
          issue = "Overwatering / Root Rot"
          severity = "High"
          advice =
            "Reduce watering frequency, ensure good drainage. Check roots for rot and prune affected parts. Repot if necessary with fresh, well-draining soil."
          icon = <XCircle className="h-8 w-8 text-red-500" />
        } else if (
          (selectedSymptoms.includes("Brown Spots") && selectedSymptoms.includes("Pests Visible")) ||
          remarks.toLowerCase().includes("bugs") ||
          remarks.toLowerCase().includes("spider mites")
        ) {
          issue = "Pest Infestation (e.g., Spider Mites)"
          severity = "Medium"
          advice =
            "Isolate the plant immediately. Wipe leaves with neem oil solution or insecticidal soap. Increase humidity. Repeat treatment every few days until pests are gone."
          icon = <AlertTriangle className="h-8 w-8 text-orange-500" />
        } else if (selectedSymptoms.includes("Yellowing Leaves") || remarks.toLowerCase().includes("yellow")) {
          issue = "Nutrient Deficiency / Underwatering"
          severity = "Low"
          advice =
            "Check soil moisture deeply. If dry, water thoroughly. Consider fertilizing with a balanced liquid plant food according to product instructions."
          icon = <AlertTriangle className="h-8 w-8 text-orange-500" />
        } else if (selectedSymptoms.includes("Stunted Growth") || remarks.toLowerCase().includes("not growing")) {
          issue = "Lack of Nutrients / Insufficient Light"
          severity = "Low"
          advice =
            "Ensure adequate light exposure. Fertilize with a balanced plant food. Check for proper pot size and consider repotting if root-bound."
          icon = <AlertTriangle className="h-8 w-8 text-orange-500" />
        }

        setDiagnosisResult({
          issue,
          severity,
          advice,
          icon,
          input: combinedInput,
        })
        setIsLoadingDiagnosis(false)
      }, 2000)
    }

    const isDiagnosisDisabled = selectedSymptoms.length === 0 && !photoFile && remarks.trim() === ""

    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
        {/* Enhanced Header */}
        <div className="bg-white/80 backdrop-blur-xl border-b border-gray-100/50 px-6 pt-14 pb-6">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentScreen("home")}
              className="text-gray-600 hover:bg-gray-100 rounded-full px-4"
            >
              ← Back
            </Button>
            <h1 className="text-lg font-bold text-gray-900">Diagnose Plant Issue</h1>
            <div className="w-10" />
          </div>
        </div>

        <div className="px-6 py-8 space-y-8 pb-24">
          {/* Enhanced Photo Input */}
          <Card className="border-0 shadow-xl shadow-gray-200/50 bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-gray-900">Add Photo</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {photoFile && (
                <div className="relative w-full h-56 rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center shadow-inner">
                  <Image
                    src={photoFile || "/placeholder.svg"}
                    alt="Plant issue"
                    width={200}
                    height={200}
                    className="object-cover w-full h-full"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white"
                    onClick={() => setPhotoFile(null)}
                  >
                    <XCircle className="h-5 w-5 text-gray-600" />
                  </Button>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  className="h-14 border-2 border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50 rounded-2xl flex items-center justify-center gap-3 hover:from-orange-100 hover:to-amber-100 font-semibold text-orange-700"
                  onClick={handleTakePhoto}
                >
                  <Camera className="h-5 w-5" /> Take Photo
                </Button>
                <Button
                  variant="outline"
                  className="h-14 border-2 border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50 rounded-2xl flex items-center justify-center gap-3 hover:from-orange-100 hover:to-amber-100 font-semibold text-orange-700"
                  onClick={handleUploadPhoto}
                >
                  <Upload className="h-5 w-5" /> Upload Photo
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Symptom Selection */}
          <Card className="border-0 shadow-xl shadow-gray-200/50 bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-gray-900">Select Common Symptoms</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-wrap gap-3">
              {commonSymptoms.map((symptom) => (
                <Badge
                  key={symptom}
                  variant={selectedSymptoms.includes(symptom) ? "default" : "outline"}
                  onClick={() => toggleSymptom(symptom)}
                  className={`cursor-pointer px-5 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                    selectedSymptoms.includes(symptom)
                      ? "bg-gradient-to-r from-orange-500 to-amber-500 text-white hover:from-orange-600 hover:to-amber-600 shadow-lg shadow-orange-500/25"
                      : "border-2 border-orange-200 text-orange-700 bg-gradient-to-r from-orange-50 to-amber-50 hover:from-orange-100 hover:to-amber-100"
                  }`}
                >
                  {symptom}
                </Badge>
              ))}
            </CardContent>
          </Card>

          {/* Enhanced Remarks Textarea */}
          <Card className="border-0 shadow-xl shadow-gray-200/50 bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-gray-900">Describe Symptoms / Remarks</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="e.g., 'Leaves are turning yellow from the bottom up and feel mushy. I think I might be overwatering it.'"
                value={remarks}
                onChange={(e) => setRemarks(e.target.value)}
                rows={4}
                className="min-h-[120px] rounded-2xl border-2 border-gray-200 focus:border-orange-500 bg-white/80 resize-none text-base"
              />
            </CardContent>
          </Card>

          <Button
            onClick={runDiagnosis}
            disabled={isDiagnosisDisabled || isLoadingDiagnosis}
            className="w-full h-14 bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700 text-white rounded-2xl font-semibold shadow-lg shadow-orange-500/25 transition-all duration-200 active:scale-95 disabled:opacity-50"
          >
            {isLoadingDiagnosis ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin mr-3" /> Analyzing...
              </>
            ) : (
              "Run Diagnosis"
            )}
          </Button>

          {/* Enhanced Diagnosis Result */}
          {diagnosisResult && (
            <Card className="border-0 shadow-2xl shadow-gray-200/60 bg-white/90 backdrop-blur-sm rounded-3xl overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between pb-4">
                <CardTitle className="text-xl font-bold text-gray-900">Diagnosis Result</CardTitle>
                {diagnosisResult.icon}
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="font-bold text-gray-900 text-2xl mb-2">{diagnosisResult.issue}</h3>
                  <Badge
                    className={`font-medium ${
                      diagnosisResult.severity === "High"
                        ? "bg-red-100 text-red-800"
                        : diagnosisResult.severity === "Medium"
                          ? "bg-orange-100 text-orange-800"
                          : "bg-emerald-100 text-emerald-800"
                    }`}
                  >
                    Severity: {diagnosisResult.severity}
                  </Badge>
                </div>
                <div className="p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl">
                  <h4 className="font-bold text-gray-800 text-lg mb-3">Treatment Advice:</h4>
                  <p className="text-gray-700 leading-relaxed">{diagnosisResult.advice}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    )
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case "home":
        return <HomeScreen />
      case "camera":
        return <CameraScreen />
      case "results":
        return <ResultsScreen />
      case "collection":
        return <CollectionScreen />
      case "diagnose":
        return <DiagnoseScreen />
      default:
        return <HomeScreen />
    }
  }

  return (
    <div className="max-w-sm mx-auto bg-white min-h-screen relative overflow-hidden">
      {renderScreen()}

      {/* Enhanced Bottom Navigation */}
      {currentScreen !== "camera" && currentScreen !== "results" && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm">
          <div className="bg-white/90 backdrop-blur-xl border-t border-gray-200/50 shadow-2xl shadow-gray-200/50">
            <div className="flex items-center justify-around py-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentScreen("home")}
                className={`flex flex-col items-center py-3 px-4 rounded-2xl transition-all duration-200 ${
                  currentScreen === "home"
                    ? "text-emerald-600 bg-emerald-50"
                    : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                }`}
              >
                <Home className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">Home</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentScreen("camera")}
                className="flex flex-col items-center py-3 px-4 rounded-2xl text-gray-500 hover:text-gray-700 hover:bg-gray-50 transition-all duration-200"
              >
                <Camera className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">Scan</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentScreen("collection")}
                className={`flex flex-col items-center py-3 px-4 rounded-2xl transition-all duration-200 ${
                  currentScreen === "collection"
                    ? "text-emerald-600 bg-emerald-50"
                    : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                }`}
              >
                <Leaf className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">My Plants</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentScreen("profile")}
                className={`flex flex-col items-center py-3 px-4 rounded-2xl transition-all duration-200 ${
                  currentScreen === "profile"
                    ? "text-emerald-600 bg-emerald-50"
                    : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                }`}
              >
                <User className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">Profile</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
