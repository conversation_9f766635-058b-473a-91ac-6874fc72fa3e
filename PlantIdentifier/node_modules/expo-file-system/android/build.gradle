plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '18.1.11'

android {
  namespace "expo.modules.filesystem"
  defaultConfig {
    versionCode 30
    versionName "18.1.11"
  }
}

dependencies {
  api 'commons-codec:commons-codec:1.10'
  api 'commons-io:commons-io:1.4'
  api 'com.squareup.okhttp3:okhttp:4.9.2'
  api 'com.squareup.okhttp3:okhttp-urlconnection:4.9.2'
  api 'com.squareup.okio:okio:2.9.0'
  api "androidx.legacy:legacy-support-v4:1.0.0"
}
