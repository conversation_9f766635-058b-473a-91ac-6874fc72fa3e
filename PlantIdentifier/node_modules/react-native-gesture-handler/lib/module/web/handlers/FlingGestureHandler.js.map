{"version": 3, "names": ["State", "DiagonalDirections", "Directions", "Gesture<PERSON>andler", "Vector", "coneToDeviation", "DEFAULT_MAX_DURATION_MS", "DEFAULT_MIN_VELOCITY", "DEFAULT_ALIGNMENT_CONE", "DEFAULT_DIRECTION", "RIGHT", "DEFAULT_NUMBER_OF_TOUCHES_REQUIRED", "AXIAL_DEVIATION_COSINE", "DIAGONAL_DEVIATION_COSINE", "FlingGestureHandler", "numberOfPointersRequired", "direction", "maxDurationMs", "minVelocity", "maxNumberOfPointersSimultaneously", "keyPointer", "NaN", "updateGestureConfig", "enabled", "props", "config", "numberOfPointers", "startFling", "begin", "delayTimeout", "setTimeout", "fail", "tryEndFling", "velocityVector", "fromVelocity", "tracker", "getAlignment", "minimalAlignmentCosine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fromDirection", "axialDirectionsList", "Object", "values", "diagonalDirectionsList", "axialAlignmentList", "map", "diagonalAlignmentList", "isAligned", "some", "Boolean", "isFast", "magnitude", "clearTimeout", "activate", "endFling", "onPointerDown", "event", "isButtonInConfig", "button", "addToTracker", "pointerId", "newPointerAction", "tryToSendTouchEvent", "onPointerAdd", "state", "UNDETERMINED", "BEGAN", "trackedPointersCount", "pointerMoveAction", "track", "onPointerMove", "onPointerOutOfBounds", "onPointerUp", "onUp", "onPointerRemove", "removeFromTracker", "force", "end", "resetConfig"], "sourceRoot": "../../../../src", "sources": ["web/handlers/FlingGestureHandler.ts"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,kBAAkB,EAAEC,UAAU,QAAQ,kBAAkB;AAGjE,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAASC,eAAe,QAAQ,UAAU;AAE1C,MAAMC,uBAAuB,GAAG,GAAG;AACnC,MAAMC,oBAAoB,GAAG,GAAG;AAChC,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,iBAAiB,GAAGP,UAAU,CAACQ,KAAK;AAC1C,MAAMC,kCAAkC,GAAG,CAAC;AAE5C,MAAMC,sBAAsB,GAAGP,eAAe,CAACG,sBAAsB,CAAC;AACtE,MAAMK,yBAAyB,GAAGR,eAAe,CAAC,EAAE,GAAGG,sBAAsB,CAAC;AAE9E,eAAe,MAAMM,mBAAmB,SAASX,cAAc,CAAC;EACtDY,wBAAwB,GAAGJ,kCAAkC;EAC7DK,SAAS,GAAeP,iBAAiB;EAEzCQ,aAAa,GAAGX,uBAAuB;EACvCY,WAAW,GAAGX,oBAAoB;EAGlCY,iCAAiC,GAAG,CAAC;EACrCC,UAAU,GAAGC,GAAG;EAEjBC,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAc,CAAC,EAAQ;IACrE,KAAK,CAACF,mBAAmB,CAAC;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IAEzD,IAAI,IAAI,CAACC,MAAM,CAACT,SAAS,EAAE;MACzB,IAAI,CAACA,SAAS,GAAG,IAAI,CAACS,MAAM,CAACT,SAAS;IACxC;IAEA,IAAI,IAAI,CAACS,MAAM,CAACC,gBAAgB,EAAE;MAChC,IAAI,CAACX,wBAAwB,GAAG,IAAI,CAACU,MAAM,CAACC,gBAAgB;IAC9D;EACF;EAEQC,UAAUA,CAAA,EAAS;IACzB,IAAI,CAACC,KAAK,CAAC,CAAC;IAEZ,IAAI,CAACT,iCAAiC,GAAG,CAAC;IAE1C,IAAI,CAACU,YAAY,GAAGC,UAAU,CAAC,MAAM,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACd,aAAa,CAAC;EACvE;EAEQe,WAAWA,CAAA,EAAY;IAC7B,MAAMC,cAAc,GAAG7B,MAAM,CAAC8B,YAAY,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAACf,UAAU,CAAC;IAEzE,IAAI,CAACa,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;IAEA,MAAMG,YAAY,GAAGA,CACnBpB,SAA0C,EAC1CqB,sBAA8B,KAC3B;MACH,OACE,CAACrB,SAAS,GAAG,IAAI,CAACA,SAAS,MAAMA,SAAS,IAC1CiB,cAAc,CAACK,SAAS,CACtBlC,MAAM,CAACmC,aAAa,CAACvB,SAAS,CAAC,EAC/BqB,sBACF,CAAC;IAEL,CAAC;IAED,MAAMG,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAACxC,UAAU,CAAC;IACrD,MAAMyC,sBAAsB,GAAGF,MAAM,CAACC,MAAM,CAACzC,kBAAkB,CAAC;;IAEhE;IACA,MAAM2C,kBAAkB,GAAGJ,mBAAmB,CAACK,GAAG,CAAE7B,SAAS,IAC3DoB,YAAY,CAACpB,SAAS,EAAEJ,sBAAsB,CAChD,CAAC;IAED,MAAMkC,qBAAqB,GAAGH,sBAAsB,CAACE,GAAG,CAAE7B,SAAS,IACjEoB,YAAY,CAACpB,SAAS,EAAEH,yBAAyB,CACnD,CAAC;IAED,MAAMkC,SAAS,GACbH,kBAAkB,CAACI,IAAI,CAACC,OAAO,CAAC,IAAIH,qBAAqB,CAACE,IAAI,CAACC,OAAO,CAAC;IAEzE,MAAMC,MAAM,GAAGjB,cAAc,CAACkB,SAAS,GAAG,IAAI,CAACjC,WAAW;IAE1D,IACE,IAAI,CAACC,iCAAiC,KACpC,IAAI,CAACJ,wBAAwB,IAC/BgC,SAAS,IACTG,MAAM,EACN;MACAE,YAAY,CAAC,IAAI,CAACvB,YAAY,CAAC;MAC/B,IAAI,CAACwB,QAAQ,CAAC,CAAC;MAEf,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEQC,QAAQA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACtB,WAAW,CAAC,CAAC,EAAE;MACvB,IAAI,CAACD,IAAI,CAAC,CAAC;IACb;EACF;EAEUwB,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;MACxC;IACF;IAEA,IAAI,CAACvB,OAAO,CAACwB,YAAY,CAACH,KAAK,CAAC;IAChC,IAAI,CAACpC,UAAU,GAAGoC,KAAK,CAACI,SAAS;IAEjC,KAAK,CAACL,aAAa,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACK,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACC,mBAAmB,CAACN,KAAK,CAAC;EACjC;EAEUO,YAAYA,CAACP,KAAmB,EAAQ;IAChD,IAAI,CAACrB,OAAO,CAACwB,YAAY,CAACH,KAAK,CAAC;IAChC,KAAK,CAACO,YAAY,CAACP,KAAK,CAAC;IACzB,IAAI,CAACK,gBAAgB,CAAC,CAAC;EACzB;EAEQA,gBAAgBA,CAAA,EAAS;IAC/B,IAAI,IAAI,CAACG,KAAK,KAAKhE,KAAK,CAACiE,YAAY,EAAE;MACrC,IAAI,CAACtC,UAAU,CAAC,CAAC;IACnB;IAEA,IAAI,IAAI,CAACqC,KAAK,KAAKhE,KAAK,CAACkE,KAAK,EAAE;MAC9B;IACF;IAEA,IAAI,CAAClC,WAAW,CAAC,CAAC;IAElB,IACE,IAAI,CAACG,OAAO,CAACgC,oBAAoB,GAAG,IAAI,CAAChD,iCAAiC,EAC1E;MACA,IAAI,CAACA,iCAAiC,GACpC,IAAI,CAACgB,OAAO,CAACgC,oBAAoB;IACrC;EACF;EAEQC,iBAAiBA,CAACZ,KAAmB,EAAQ;IACnD,IAAI,CAACrB,OAAO,CAACkC,KAAK,CAACb,KAAK,CAAC;IAEzB,IAAI,IAAI,CAACQ,KAAK,KAAKhE,KAAK,CAACkE,KAAK,EAAE;MAC9B;IACF;IAEA,IAAI,CAAClC,WAAW,CAAC,CAAC;EACpB;EAEUsC,aAAaA,CAACd,KAAmB,EAAQ;IACjD,IAAI,CAACY,iBAAiB,CAACZ,KAAK,CAAC;IAC7B,KAAK,CAACc,aAAa,CAACd,KAAK,CAAC;EAC5B;EAEUe,oBAAoBA,CAACf,KAAmB,EAAQ;IACxD,IAAI,CAACY,iBAAiB,CAACZ,KAAK,CAAC;IAC7B,KAAK,CAACe,oBAAoB,CAACf,KAAK,CAAC;EACnC;EAEUgB,WAAWA,CAAChB,KAAmB,EAAQ;IAC/C,KAAK,CAACgB,WAAW,CAAChB,KAAK,CAAC;IACxB,IAAI,CAACiB,IAAI,CAACjB,KAAK,CAAC;IAEhB,IAAI,CAACpC,UAAU,GAAGC,GAAG;EACvB;EAEUqD,eAAeA,CAAClB,KAAmB,EAAQ;IACnD,KAAK,CAACkB,eAAe,CAAClB,KAAK,CAAC;IAC5B,IAAI,CAACiB,IAAI,CAACjB,KAAK,CAAC;EAClB;EAEQiB,IAAIA,CAACjB,KAAmB,EAAQ;IACtC,IAAI,IAAI,CAACQ,KAAK,KAAKhE,KAAK,CAACkE,KAAK,EAAE;MAC9B,IAAI,CAACZ,QAAQ,CAAC,CAAC;IACjB;IAEA,IAAI,CAACnB,OAAO,CAACwC,iBAAiB,CAACnB,KAAK,CAACI,SAAS,CAAC;EACjD;EAEOP,QAAQA,CAACuB,KAAe,EAAQ;IACrC,KAAK,CAACvB,QAAQ,CAACuB,KAAK,CAAC;IACrB,IAAI,CAACC,GAAG,CAAC,CAAC;EACZ;EAEUC,WAAWA,CAAA,EAAS;IAC5B,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAAC/D,wBAAwB,GAAGJ,kCAAkC;IAClE,IAAI,CAACK,SAAS,GAAGP,iBAAiB;EACpC;AACF", "ignoreList": []}