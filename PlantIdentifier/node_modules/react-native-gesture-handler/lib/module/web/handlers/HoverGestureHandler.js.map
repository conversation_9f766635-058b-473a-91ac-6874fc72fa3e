{"version": 3, "names": ["State", "GestureHandlerOrchestrator", "Gesture<PERSON>andler", "HoverGestureHandler", "transformNativeEvent", "stylusData", "onPointerMoveOver", "event", "instance", "recordHandlerIfNotPresent", "tracker", "addToTracker", "state", "UNDETERMINED", "begin", "activate", "onPointerMoveOut", "removeFromTracker", "pointerId", "end", "onPointerMove", "track", "onPointerCancel", "reset"], "sourceRoot": "../../../../src", "sources": ["web/handlers/HoverGestureHandler.ts"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,aAAa;AAEnC,OAAOC,0BAA0B,MAAM,qCAAqC;AAC5E,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,eAAe,MAAMC,mBAAmB,SAASD,cAAc,CAAC;EAGpDE,oBAAoBA,CAAA,EAA4B;IACxD,OAAO;MACL,GAAG,KAAK,CAACA,oBAAoB,CAAC,CAAC;MAC/BC,UAAU,EAAE,IAAI,CAACA;IACnB,CAAC;EACH;EAEUC,iBAAiBA,CAACC,KAAmB,EAAQ;IACrDN,0BAA0B,CAACO,QAAQ,CAACC,yBAAyB,CAAC,IAAI,CAAC;IAEnE,IAAI,CAACC,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;IAChC,IAAI,CAACF,UAAU,GAAGE,KAAK,CAACF,UAAU;IAClC,KAAK,CAACC,iBAAiB,CAACC,KAAK,CAAC;IAE9B,IAAI,IAAI,CAACK,KAAK,KAAKZ,KAAK,CAACa,YAAY,EAAE;MACrC,IAAI,CAACC,KAAK,CAAC,CAAC;MACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;EACF;EAEUC,gBAAgBA,CAACT,KAAmB,EAAQ;IACpD,IAAI,CAACG,OAAO,CAACO,iBAAiB,CAACV,KAAK,CAACW,SAAS,CAAC;IAC/C,IAAI,CAACb,UAAU,GAAGE,KAAK,CAACF,UAAU;IAElC,KAAK,CAACW,gBAAgB,CAACT,KAAK,CAAC;IAE7B,IAAI,CAACY,GAAG,CAAC,CAAC;EACZ;EAEUC,aAAaA,CAACb,KAAmB,EAAQ;IACjD,IAAI,CAACG,OAAO,CAACW,KAAK,CAACd,KAAK,CAAC;IACzB,IAAI,CAACF,UAAU,GAAGE,KAAK,CAACF,UAAU;IAElC,KAAK,CAACe,aAAa,CAACb,KAAK,CAAC;EAC5B;EAEUe,eAAeA,CAACf,KAAmB,EAAQ;IACnD,KAAK,CAACe,eAAe,CAACf,KAAK,CAAC;IAC5B,IAAI,CAACgB,KAAK,CAAC,CAAC;EACd;AACF", "ignoreList": []}