{"version": 3, "names": ["State", "Gesture<PERSON>andler", "DEFAULT_MIN_DURATION_MS", "DEFAULT_MAX_DIST_DP", "SCALING_FACTOR", "LongPressGestureHandler", "minDurationMs", "defaultMaxDistSq", "maxDistSq", "numberOfPointers", "startX", "startY", "startTime", "previousTime", "init", "ref", "propsRef", "config", "enableContextMenu", "undefined", "transformNativeEvent", "duration", "Date", "now", "updateGestureConfig", "enabled", "props", "maxDist", "resetConfig", "onStateChange", "_newState", "_oldState", "clearTimeout", "activationTimeout", "onPointerDown", "event", "isButtonInConfig", "button", "tracker", "addToTracker", "x", "y", "tryBegin", "tryActivate", "tryToSendTouchEvent", "onPointerAdd", "trackedPointersCount", "fail", "absoluteCoordsAverage", "getAbsoluteCoordsAverage", "onPointerMove", "track", "checkDistanceFail", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "state", "ACTIVE", "end", "onPointerRemove", "UNDETERMINED", "begin", "setTimeout", "activate", "dx", "dy", "distSq", "cancel"], "sourceRoot": "../../../../src", "sources": ["web/handlers/LongPressGestureHandler.ts"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,aAAa;AAGnC,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,MAAMC,uBAAuB,GAAG,GAAG;AACnC,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,cAAc,GAAG,EAAE;AAEzB,eAAe,MAAMC,uBAAuB,SAASJ,cAAc,CAAC;EAC1DK,aAAa,GAAGJ,uBAAuB;EACvCK,gBAAgB,GAAGJ,mBAAmB,GAAGC,cAAc;EAEvDI,SAAS,GAAG,IAAI,CAACD,gBAAgB;EACjCE,gBAAgB,GAAG,CAAC;EACpBC,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,CAAC;EAEVC,SAAS,GAAG,CAAC;EACbC,YAAY,GAAG,CAAC;EAIjBC,IAAIA,CAACC,GAAW,EAAEC,QAAkC,EAAE;IAC3D,IAAI,IAAI,CAACC,MAAM,CAACC,iBAAiB,KAAKC,SAAS,EAAE;MAC/C,IAAI,CAACF,MAAM,CAACC,iBAAiB,GAAG,KAAK;IACvC;IAEA,KAAK,CAACJ,IAAI,CAACC,GAAG,EAAEC,QAAQ,CAAC;EAC3B;EAEUI,oBAAoBA,CAAA,EAAG;IAC/B,OAAO;MACL,GAAG,KAAK,CAACA,oBAAoB,CAAC,CAAC;MAC/BC,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACX;IAC9B,CAAC;EACH;EAEOY,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAc,CAAC,EAAQ;IACrE,KAAK,CAACF,mBAAmB,CAAC;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IAEzD,IAAI,IAAI,CAACT,MAAM,CAACX,aAAa,KAAKa,SAAS,EAAE;MAC3C,IAAI,CAACb,aAAa,GAAG,IAAI,CAACW,MAAM,CAACX,aAAa;IAChD;IAEA,IAAI,IAAI,CAACW,MAAM,CAACU,OAAO,KAAKR,SAAS,EAAE;MACrC,IAAI,CAACX,SAAS,GAAG,IAAI,CAACS,MAAM,CAACU,OAAO,GAAG,IAAI,CAACV,MAAM,CAACU,OAAO;IAC5D;IAEA,IAAI,IAAI,CAACV,MAAM,CAACR,gBAAgB,KAAKU,SAAS,EAAE;MAC9C,IAAI,CAACV,gBAAgB,GAAG,IAAI,CAACQ,MAAM,CAACR,gBAAgB;IACtD;EACF;EAEUmB,WAAWA,CAAA,EAAS;IAC5B,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACtB,aAAa,GAAGJ,uBAAuB;IAC5C,IAAI,CAACM,SAAS,GAAG,IAAI,CAACD,gBAAgB;EACxC;EAEUsB,aAAaA,CAACC,SAAgB,EAAEC,SAAgB,EAAQ;IAChEC,YAAY,CAAC,IAAI,CAACC,iBAAiB,CAAC;EACtC;EAEUC,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;MACxC;IACF;IAEA,IAAI,CAACC,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACzB,MAAM,GAAGyB,KAAK,CAACK,CAAC;IACrB,IAAI,CAAC7B,MAAM,GAAGwB,KAAK,CAACM,CAAC;IAErB,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,WAAW,CAAC,CAAC;IAElB,IAAI,CAACC,mBAAmB,CAACT,KAAK,CAAC;EACjC;EACUU,YAAYA,CAACV,KAAmB,EAAQ;IAChD,KAAK,CAACU,YAAY,CAACV,KAAK,CAAC;IACzB,IAAI,CAACG,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;IAEhC,IAAI,IAAI,CAACG,OAAO,CAACQ,oBAAoB,GAAG,IAAI,CAACrC,gBAAgB,EAAE;MAC7D,IAAI,CAACsC,IAAI,CAAC,CAAC;MACX;IACF;IAEA,MAAMC,qBAAqB,GAAG,IAAI,CAACV,OAAO,CAACW,wBAAwB,CAAC,CAAC;IAErE,IAAI,CAACvC,MAAM,GAAGsC,qBAAqB,CAACR,CAAC;IACrC,IAAI,CAAC7B,MAAM,GAAGqC,qBAAqB,CAACP,CAAC;IAErC,IAAI,CAACE,WAAW,CAAC,CAAC;EACpB;EAEUO,aAAaA,CAACf,KAAmB,EAAQ;IACjD,KAAK,CAACe,aAAa,CAACf,KAAK,CAAC;IAC1B,IAAI,CAACG,OAAO,CAACa,KAAK,CAAChB,KAAK,CAAC;IACzB,IAAI,CAACiB,iBAAiB,CAAC,CAAC;EAC1B;EAEUC,oBAAoBA,CAAClB,KAAmB,EAAQ;IACxD,KAAK,CAACkB,oBAAoB,CAAClB,KAAK,CAAC;IACjC,IAAI,CAACG,OAAO,CAACa,KAAK,CAAChB,KAAK,CAAC;IACzB,IAAI,CAACiB,iBAAiB,CAAC,CAAC;EAC1B;EAEUE,WAAWA,CAACnB,KAAmB,EAAQ;IAC/C,KAAK,CAACmB,WAAW,CAACnB,KAAK,CAAC;IACxB,IAAI,CAACG,OAAO,CAACiB,iBAAiB,CAACpB,KAAK,CAACqB,SAAS,CAAC;IAE/C,IAAI,IAAI,CAACC,KAAK,KAAKzD,KAAK,CAAC0D,MAAM,EAAE;MAC/B,IAAI,CAACC,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACZ,IAAI,CAAC,CAAC;IACb;EACF;EAEUa,eAAeA,CAACzB,KAAmB,EAAQ;IACnD,KAAK,CAACyB,eAAe,CAACzB,KAAK,CAAC;IAC5B,IAAI,CAACG,OAAO,CAACiB,iBAAiB,CAACpB,KAAK,CAACqB,SAAS,CAAC;IAE/C,IACE,IAAI,CAAClB,OAAO,CAACQ,oBAAoB,GAAG,IAAI,CAACrC,gBAAgB,IACzD,IAAI,CAACgD,KAAK,KAAKzD,KAAK,CAAC0D,MAAM,EAC3B;MACA,IAAI,CAACX,IAAI,CAAC,CAAC;IACb;EACF;EAEQL,QAAQA,CAAA,EAAS;IACvB,IAAI,IAAI,CAACe,KAAK,KAAKzD,KAAK,CAAC6D,YAAY,EAAE;MACrC;IACF;IAEA,IAAI,CAAChD,YAAY,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACX,SAAS,GAAG,IAAI,CAACC,YAAY;IAElC,IAAI,CAACiD,KAAK,CAAC,CAAC;EACd;EAEQnB,WAAWA,CAAA,EAAS;IAC1B,IAAI,IAAI,CAACL,OAAO,CAACQ,oBAAoB,KAAK,IAAI,CAACrC,gBAAgB,EAAE;MAC/D;IACF;IAEA,IAAI,IAAI,CAACH,aAAa,GAAG,CAAC,EAAE;MAC1B,IAAI,CAAC2B,iBAAiB,GAAG8B,UAAU,CAAC,MAAM;QACxC,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC1D,aAAa,CAAC;IACxB,CAAC,MAAM,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,EAAE;MACnC,IAAI,CAAC0D,QAAQ,CAAC,CAAC;IACjB;EACF;EAEQZ,iBAAiBA,CAAA,EAAS;IAChC,MAAMJ,qBAAqB,GAAG,IAAI,CAACV,OAAO,CAACW,wBAAwB,CAAC,CAAC;IAErE,MAAMgB,EAAE,GAAGjB,qBAAqB,CAACR,CAAC,GAAG,IAAI,CAAC9B,MAAM;IAChD,MAAMwD,EAAE,GAAGlB,qBAAqB,CAACP,CAAC,GAAG,IAAI,CAAC9B,MAAM;IAChD,MAAMwD,MAAM,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAEhC,IAAIC,MAAM,IAAI,IAAI,CAAC3D,SAAS,EAAE;MAC5B;IACF;IAEA,IAAI,IAAI,CAACiD,KAAK,KAAKzD,KAAK,CAAC0D,MAAM,EAAE;MAC/B,IAAI,CAACU,MAAM,CAAC,CAAC;IACf,CAAC,MAAM;MACL,IAAI,CAACrB,IAAI,CAAC,CAAC;IACb;EACF;AACF", "ignoreList": []}