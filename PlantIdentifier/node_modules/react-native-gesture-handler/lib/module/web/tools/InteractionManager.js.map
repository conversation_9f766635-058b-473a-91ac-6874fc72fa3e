{"version": 3, "names": ["InteractionManager", "waitForRelations", "Map", "simultaneousRelations", "blocksHandlersRelations", "constructor", "configureInteractions", "handler", "config", "dropRelationsForHandlerWithTag", "handlerTag", "waitFor", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "push", "set", "simultaneousHandlers", "blocksHandlers", "shouldWaitForHandlerFailure", "get", "find", "tag", "undefined", "shouldRecognizeSimultaneously", "shouldRequireHandlerToWaitForFailure", "shouldHandlerBeCancelledBy", "_handler", "isNativeHandler", "name", "isActive", "active", "isButton", "delete", "reset", "clear", "instance", "_instance"], "sourceRoot": "../../../../src", "sources": ["web/tools/InteractionManager.ts"], "mappings": ";;AAGA,eAAe,MAAMA,kBAAkB,CAAC;EAErBC,gBAAgB,GAA0B,IAAIC,GAAG,CAAC,CAAC;EACnDC,qBAAqB,GAA0B,IAAID,GAAG,CAAC,CAAC;EACxDE,uBAAuB,GAA0B,IAAIF,GAAG,CAAC,CAAC;;EAE3E;EACA;EACQG,WAAWA,CAAA,EAAG,CAAC;EAEhBC,qBAAqBA,CAACC,OAAwB,EAAEC,MAAc,EAAE;IACrE,IAAI,CAACC,8BAA8B,CAACF,OAAO,CAACG,UAAU,CAAC;IAEvD,IAAIF,MAAM,CAACG,OAAO,EAAE;MAClB,MAAMA,OAAiB,GAAG,EAAE;MAC5BH,MAAM,CAACG,OAAO,CAACC,OAAO,CAAEC,YAAqB,IAAW;QACtD;QACA,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;UACpCF,OAAO,CAACG,IAAI,CAACD,YAAY,CAAC;QAC5B,CAAC,MAAM;UACL;UACAF,OAAO,CAACG,IAAI,CAACD,YAAY,CAACH,UAAU,CAAC;QACvC;MACF,CAAC,CAAC;MAEF,IAAI,CAACT,gBAAgB,CAACc,GAAG,CAACR,OAAO,CAACG,UAAU,EAAEC,OAAO,CAAC;IACxD;IAEA,IAAIH,MAAM,CAACQ,oBAAoB,EAAE;MAC/B,MAAMA,oBAA8B,GAAG,EAAE;MACzCR,MAAM,CAACQ,oBAAoB,CAACJ,OAAO,CAAEC,YAAqB,IAAW;QACnE,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;UACpCG,oBAAoB,CAACF,IAAI,CAACD,YAAY,CAAC;QACzC,CAAC,MAAM;UACLG,oBAAoB,CAACF,IAAI,CAACD,YAAY,CAACH,UAAU,CAAC;QACpD;MACF,CAAC,CAAC;MAEF,IAAI,CAACP,qBAAqB,CAACY,GAAG,CAACR,OAAO,CAACG,UAAU,EAAEM,oBAAoB,CAAC;IAC1E;IAEA,IAAIR,MAAM,CAACS,cAAc,EAAE;MACzB,MAAMA,cAAwB,GAAG,EAAE;MACnCT,MAAM,CAACS,cAAc,CAACL,OAAO,CAAEC,YAAqB,IAAW;QAC7D,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;UACpCI,cAAc,CAACH,IAAI,CAACD,YAAY,CAAC;QACnC,CAAC,MAAM;UACLI,cAAc,CAACH,IAAI,CAACD,YAAY,CAACH,UAAU,CAAC;QAC9C;MACF,CAAC,CAAC;MAEF,IAAI,CAACN,uBAAuB,CAACW,GAAG,CAACR,OAAO,CAACG,UAAU,EAAEO,cAAc,CAAC;IACtE;EACF;EAEOC,2BAA2BA,CAChCX,OAAwB,EACxBM,YAA6B,EACpB;IACT,MAAMF,OAA6B,GAAG,IAAI,CAACV,gBAAgB,CAACkB,GAAG,CAC7DZ,OAAO,CAACG,UACV,CAAC;IAED,OACEC,OAAO,EAAES,IAAI,CAAEC,GAAW,IAAK;MAC7B,OAAOA,GAAG,KAAKR,YAAY,CAACH,UAAU;IACxC,CAAC,CAAC,KAAKY,SAAS;EAEpB;EAEOC,6BAA6BA,CAClChB,OAAwB,EACxBM,YAA6B,EACpB;IACT,MAAMG,oBAA0C,GAC9C,IAAI,CAACb,qBAAqB,CAACgB,GAAG,CAACZ,OAAO,CAACG,UAAU,CAAC;IAEpD,OACEM,oBAAoB,EAAEI,IAAI,CAAEC,GAAW,IAAK;MAC1C,OAAOA,GAAG,KAAKR,YAAY,CAACH,UAAU;IACxC,CAAC,CAAC,KAAKY,SAAS;EAEpB;EAEOE,oCAAoCA,CACzCjB,OAAwB,EACxBM,YAA6B,EACpB;IACT,MAAMF,OAA6B,GAAG,IAAI,CAACP,uBAAuB,CAACe,GAAG,CACpEZ,OAAO,CAACG,UACV,CAAC;IAED,OACEC,OAAO,EAAES,IAAI,CAAEC,GAAW,IAAK;MAC7B,OAAOA,GAAG,KAAKR,YAAY,CAACH,UAAU;IACxC,CAAC,CAAC,KAAKY,SAAS;EAEpB;EAEOG,0BAA0BA,CAC/BC,QAAyB,EACzBb,YAA6B,EACpB;IACT;IACA,MAAMc,eAAe,GACnBd,YAAY,CAACR,WAAW,CAACuB,IAAI,KAAK,0BAA0B;IAC9D,MAAMC,QAAQ,GAAGhB,YAAY,CAACiB,MAAM;IACpC,MAAMC,QAAQ,GAAGlB,YAAY,CAACkB,QAAQ,GAAG,CAAC,KAAK,IAAI;IAEnD,OAAOJ,eAAe,IAAIE,QAAQ,IAAI,CAACE,QAAQ;EACjD;EAEOtB,8BAA8BA,CAACC,UAAkB,EAAQ;IAC9D,IAAI,CAACT,gBAAgB,CAAC+B,MAAM,CAACtB,UAAU,CAAC;IACxC,IAAI,CAACP,qBAAqB,CAAC6B,MAAM,CAACtB,UAAU,CAAC;IAC7C,IAAI,CAACN,uBAAuB,CAAC4B,MAAM,CAACtB,UAAU,CAAC;EACjD;EAEOuB,KAAKA,CAAA,EAAG;IACb,IAAI,CAAChC,gBAAgB,CAACiC,KAAK,CAAC,CAAC;IAC7B,IAAI,CAAC/B,qBAAqB,CAAC+B,KAAK,CAAC,CAAC;IAClC,IAAI,CAAC9B,uBAAuB,CAAC8B,KAAK,CAAC,CAAC;EACtC;EAEA,WAAkBC,QAAQA,CAAA,EAAuB;IAC/C,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAG,IAAIpC,kBAAkB,CAAC,CAAC;IAC3C;IAEA,OAAO,IAAI,CAACoC,SAAS;EACvB;AACF", "ignoreList": []}