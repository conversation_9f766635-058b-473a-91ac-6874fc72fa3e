{"version": 3, "names": ["EventManager", "MouseB<PERSON>on", "EventTypes", "PointerTypeMapping", "calculateViewScale", "tryExtractStylusData", "isPointerInBounds", "PointerType", "POINTER_CAPTURE_EXCLUDE_LIST", "Set", "PointerEventManager", "trackedPointers", "mouseButtonsMapper", "Map", "constructor", "view", "set", "LEFT", "MIDDLE", "RIGHT", "BUTTON_4", "BUTTON_5", "lastPosition", "x", "Infinity", "y", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "clientX", "clientY", "adaptedEvent", "mapEvent", "DOWN", "target", "has", "tagName", "setPointerCapture", "pointerId", "markAsInBounds", "add", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "UP", "releasePointerCapture", "markAsOutOfBounds", "delete", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "MOVE", "hasPointerCapture", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "onPointerOutOfBounds", "pointerCancelCallback", "CANCEL", "onPointerCancel", "clear", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "onPointerMoveOver", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onPointerMoveOut", "lostPointerCaptureCallback", "registerListeners", "addEventListener", "unregisterListeners", "removeEventListener", "rect", "getBoundingClientRect", "scaleX", "scaleY", "offsetX", "left", "offsetY", "top", "pointerType", "get", "OTHER", "button", "time", "timeStamp", "stylusData", "resetManager"], "sourceRoot": "../../../../src", "sources": ["web/tools/PointerEventManager.ts"], "mappings": ";;AAAA,OAAOA,YAAY,MAAM,gBAAgB;AACzC,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAAuBC,UAAU,QAAe,eAAe;AAC/D,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,oBAAoB,EACpBC,iBAAiB,QACZ,UAAU;AACjB,SAASC,WAAW,QAAQ,mBAAmB;AAE/C,MAAMC,4BAA4B,GAAG,IAAIC,GAAG,CAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAEzE,eAAe,MAAMC,mBAAmB,SAASV,YAAY,CAAc;EACjEW,eAAe,GAAG,IAAIF,GAAG,CAAS,CAAC;EAC1BG,kBAAkB,GAAG,IAAIC,GAAG,CAAsB,CAAC;EAGpEC,WAAWA,CAACC,IAAiB,EAAE;IAC7B,KAAK,CAACA,IAAI,CAAC;IAEX,IAAI,CAACH,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEf,WAAW,CAACgB,IAAI,CAAC;IAChD,IAAI,CAACL,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEf,WAAW,CAACiB,MAAM,CAAC;IAClD,IAAI,CAACN,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEf,WAAW,CAACkB,KAAK,CAAC;IACjD,IAAI,CAACP,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEf,WAAW,CAACmB,QAAQ,CAAC;IACpD,IAAI,CAACR,kBAAkB,CAACI,GAAG,CAAC,CAAC,EAAEf,WAAW,CAACoB,QAAQ,CAAC;IAEpD,IAAI,CAACC,YAAY,GAAG;MAClBC,CAAC,EAAE,CAACC,QAAQ;MACZC,CAAC,EAAE,CAACD;IACN,CAAC;EACH;EAEQE,mBAAmB,GAAIC,KAAmB,IAAK;IACrD,IAAI,CAACrB,iBAAiB,CAAC,IAAI,CAACS,IAAI,EAAE;MAAEQ,CAAC,EAAEI,KAAK,CAACC,OAAO;MAAEH,CAAC,EAAEE,KAAK,CAACE;IAAQ,CAAC,CAAC,EAAE;MACzE;IACF;IAEA,MAAMC,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,EAAEzB,UAAU,CAAC8B,IAAI,CAAC;IACxE,MAAMC,MAAM,GAAGN,KAAK,CAACM,MAAqB;IAE1C,IAAI,CAACzB,4BAA4B,CAAC0B,GAAG,CAACD,MAAM,CAACE,OAAO,CAAC,EAAE;MACrDF,MAAM,CAACG,iBAAiB,CAACN,YAAY,CAACO,SAAS,CAAC;IAClD;IAEA,IAAI,CAACC,cAAc,CAACR,YAAY,CAACO,SAAS,CAAC;IAC3C,IAAI,CAAC1B,eAAe,CAAC4B,GAAG,CAACT,YAAY,CAACO,SAAS,CAAC;IAEhD,IAAI,EAAE,IAAI,CAACG,qBAAqB,GAAG,CAAC,EAAE;MACpCV,YAAY,CAACW,SAAS,GAAGvC,UAAU,CAACwC,uBAAuB;MAC3D,IAAI,CAACC,YAAY,CAACb,YAAY,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAACc,aAAa,CAACd,YAAY,CAAC;IAClC;EACF,CAAC;EAEOe,iBAAiB,GAAIlB,KAAmB,IAAK;IACnD;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACa,qBAAqB,KAAK,CAAC,EAAE;MACpC;IACF;IAEA,MAAMV,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,EAAEzB,UAAU,CAAC4C,EAAE,CAAC;IACtE,MAAMb,MAAM,GAAGN,KAAK,CAACM,MAAqB;IAE1C,IAAI,CAACzB,4BAA4B,CAAC0B,GAAG,CAACD,MAAM,CAACE,OAAO,CAAC,EAAE;MACrDF,MAAM,CAACc,qBAAqB,CAACjB,YAAY,CAACO,SAAS,CAAC;IACtD;IAEA,IAAI,CAACW,iBAAiB,CAAClB,YAAY,CAACO,SAAS,CAAC;IAC9C,IAAI,CAAC1B,eAAe,CAACsC,MAAM,CAACnB,YAAY,CAACO,SAAS,CAAC;IAEnD,IAAI,EAAE,IAAI,CAACG,qBAAqB,GAAG,CAAC,EAAE;MACpCV,YAAY,CAACW,SAAS,GAAGvC,UAAU,CAACgD,qBAAqB;MACzD,IAAI,CAACC,eAAe,CAACrB,YAAY,CAAC;IACpC,CAAC,MAAM;MACL,IAAI,CAACsB,WAAW,CAACtB,YAAY,CAAC;IAChC;EACF,CAAC;EAEOuB,mBAAmB,GAAI1B,KAAmB,IAAK;IACrD,MAAMG,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,EAAEzB,UAAU,CAACoD,IAAI,CAAC;IACxE,MAAMrB,MAAM,GAAGN,KAAK,CAACM,MAAqB;;IAE1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IACE,CAACA,MAAM,EAAEsB,iBAAiB,CAAC5B,KAAK,CAACU,SAAS,CAAC,IAC3C,CAAC7B,4BAA4B,CAAC0B,GAAG,CAACD,MAAM,CAACE,OAAO,CAAC,EACjD;MACAF,MAAM,CAACG,iBAAiB,CAACT,KAAK,CAACU,SAAS,CAAC;IAC3C;IAEA,MAAMmB,QAAiB,GAAGlD,iBAAiB,CAAC,IAAI,CAACS,IAAI,EAAE;MACrDQ,CAAC,EAAEO,YAAY,CAACP,CAAC;MACjBE,CAAC,EAAEK,YAAY,CAACL;IAClB,CAAC,CAAC;IAEF,MAAMgC,YAAoB,GAAG,IAAI,CAACC,gBAAgB,CAACC,OAAO,CACxD7B,YAAY,CAACO,SACf,CAAC;IAED,IAAImB,QAAQ,EAAE;MACZ,IAAIC,YAAY,GAAG,CAAC,EAAE;QACpB3B,YAAY,CAACW,SAAS,GAAGvC,UAAU,CAAC0D,KAAK;QACzC,IAAI,CAACC,cAAc,CAAC/B,YAAY,CAAC;QACjC,IAAI,CAACQ,cAAc,CAACR,YAAY,CAACO,SAAS,CAAC;MAC7C,CAAC,MAAM;QACL,IAAI,CAACyB,aAAa,CAAChC,YAAY,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI2B,YAAY,IAAI,CAAC,EAAE;QACrB3B,YAAY,CAACW,SAAS,GAAGvC,UAAU,CAAC6D,KAAK;QACzC,IAAI,CAACC,cAAc,CAAClC,YAAY,CAAC;QACjC,IAAI,CAACkB,iBAAiB,CAAClB,YAAY,CAACO,SAAS,CAAC;MAChD,CAAC,MAAM;QACL,IAAI,CAAC4B,oBAAoB,CAACnC,YAAY,CAAC;MACzC;IACF;IAEA,IAAI,CAACR,YAAY,CAACC,CAAC,GAAGI,KAAK,CAACJ,CAAC;IAC7B,IAAI,CAACD,YAAY,CAACG,CAAC,GAAGE,KAAK,CAACF,CAAC;EAC/B,CAAC;EAEOyC,qBAAqB,GAAIvC,KAAmB,IAAK;IACvD,MAAMG,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,EAAEzB,UAAU,CAACiE,MAAM,CAAC;IAE1E,IAAI,CAACC,eAAe,CAACtC,YAAY,CAAC;IAClC,IAAI,CAACkB,iBAAiB,CAAClB,YAAY,CAACO,SAAS,CAAC;IAC9C,IAAI,CAACG,qBAAqB,GAAG,CAAC;IAC9B,IAAI,CAAC7B,eAAe,CAAC0D,KAAK,CAAC,CAAC;EAC9B,CAAC;EAEOC,oBAAoB,GAAI3C,KAAmB,IAAK;IACtD,MAAMG,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,EAAEzB,UAAU,CAAC0D,KAAK,CAAC;IAEzE,IAAI,CAACW,iBAAiB,CAACzC,YAAY,CAAC;EACtC,CAAC;EAEO0C,oBAAoB,GAAI7C,KAAmB,IAAK;IACtD,MAAMG,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,EAAEzB,UAAU,CAAC6D,KAAK,CAAC;IAEzE,IAAI,CAACU,gBAAgB,CAAC3C,YAAY,CAAC;EACrC,CAAC;EAEO4C,0BAA0B,GAAI/C,KAAmB,IAAK;IAC5D,MAAMG,YAA0B,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,EAAEzB,UAAU,CAACiE,MAAM,CAAC;IAE1E,IAAI,IAAI,CAACxD,eAAe,CAACuB,GAAG,CAACJ,YAAY,CAACO,SAAS,CAAC,EAAE;MACpD;MACA;MACA,IAAI,CAAC+B,eAAe,CAACtC,YAAY,CAAC;MAElC,IAAI,CAACU,qBAAqB,GAAG,CAAC;MAC9B,IAAI,CAAC7B,eAAe,CAAC0D,KAAK,CAAC,CAAC;IAC9B;EACF,CAAC;EAEMM,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAAC5D,IAAI,CAAC6D,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAClD,mBAAmB,CAAC;IACnE,IAAI,CAACX,IAAI,CAAC6D,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC/B,iBAAiB,CAAC;IAC/D,IAAI,CAAC9B,IAAI,CAAC6D,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACvB,mBAAmB,CAAC;IACnE,IAAI,CAACtC,IAAI,CAAC6D,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACV,qBAAqB,CAAC;;IAEvE;IACA;IACA;IACA;IACA,IAAI,CAACnD,IAAI,CAAC6D,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACN,oBAAoB,CAAC;IACrE,IAAI,CAACvD,IAAI,CAAC6D,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACJ,oBAAoB,CAAC;IACrE,IAAI,CAACzD,IAAI,CAAC6D,gBAAgB,CACxB,oBAAoB,EACpB,IAAI,CAACF,0BACP,CAAC;EACH;EAEOG,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAAC9D,IAAI,CAAC+D,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACpD,mBAAmB,CAAC;IACtE,IAAI,CAACX,IAAI,CAAC+D,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACjC,iBAAiB,CAAC;IAClE,IAAI,CAAC9B,IAAI,CAAC+D,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACzB,mBAAmB,CAAC;IACtE,IAAI,CAACtC,IAAI,CAAC+D,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACZ,qBAAqB,CAAC;IAC1E,IAAI,CAACnD,IAAI,CAAC+D,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACR,oBAAoB,CAAC;IACxE,IAAI,CAACvD,IAAI,CAAC+D,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACN,oBAAoB,CAAC;IACxE,IAAI,CAACzD,IAAI,CAAC+D,mBAAmB,CAC3B,oBAAoB,EACpB,IAAI,CAACJ,0BACP,CAAC;EACH;EAEU3C,QAAQA,CAACJ,KAAmB,EAAEc,SAAqB,EAAgB;IAC3E,MAAMsC,IAAI,GAAG,IAAI,CAAChE,IAAI,CAACiE,qBAAqB,CAAC,CAAC;IAC9C,MAAM;MAAEC,MAAM;MAAEC;IAAO,CAAC,GAAG9E,kBAAkB,CAAC,IAAI,CAACW,IAAI,CAAC;IAExD,OAAO;MACLQ,CAAC,EAAEI,KAAK,CAACC,OAAO;MAChBH,CAAC,EAAEE,KAAK,CAACE,OAAO;MAChBsD,OAAO,EAAE,CAACxD,KAAK,CAACC,OAAO,GAAGmD,IAAI,CAACK,IAAI,IAAIH,MAAM;MAC7CI,OAAO,EAAE,CAAC1D,KAAK,CAACE,OAAO,GAAGkD,IAAI,CAACO,GAAG,IAAIJ,MAAM;MAC5C7C,SAAS,EAAEV,KAAK,CAACU,SAAS;MAC1BI,SAAS,EAAEA,SAAS;MACpB8C,WAAW,EACTpF,kBAAkB,CAACqF,GAAG,CAAC7D,KAAK,CAAC4D,WAAW,CAAC,IAAIhF,WAAW,CAACkF,KAAK;MAChEC,MAAM,EAAE,IAAI,CAAC9E,kBAAkB,CAAC4E,GAAG,CAAC7D,KAAK,CAAC+D,MAAM,CAAC;MACjDC,IAAI,EAAEhE,KAAK,CAACiE,SAAS;MACrBC,UAAU,EAAExF,oBAAoB,CAACsB,KAAK;IACxC,CAAC;EACH;EAEOmE,YAAYA,CAAA,EAAS;IAC1B,KAAK,CAACA,YAAY,CAAC,CAAC;IACpB,IAAI,CAACnF,eAAe,CAAC0D,KAAK,CAAC,CAAC;EAC9B;AACF", "ignoreList": []}