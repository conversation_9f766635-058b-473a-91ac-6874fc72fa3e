{"version": 3, "names": ["findNodeHandle", "PointerEventManager", "State", "isPointerInBounds", "MouseB<PERSON>on", "KeyboardEventManager", "WheelEventManager", "GestureHandlerWebDelegate", "isInitialized", "eventManagers", "defaultViewStyles", "userSelect", "touchAction", "init", "viewRef", "handler", "Error", "handlerTag", "<PERSON><PERSON><PERSON><PERSON>", "view", "style", "config", "setUserSelect", "enabled", "setTouchAction", "setContextMenu", "push", "for<PERSON>ach", "manager", "attachEventManager", "x", "y", "measure<PERSON>iew", "rect", "getBoundingClientRect", "pageX", "left", "pageY", "top", "width", "height", "reset", "resetManager", "tryResetCursor", "activeCursor", "state", "ACTIVE", "cursor", "shouldDisableContextMenu", "enableContextMenu", "undefined", "isButtonInConfig", "RIGHT", "addContextMenuListeners", "addEventListener", "disableContextMenu", "removeContextMenuListeners", "removeEventListener", "e", "preventDefault", "stopPropagation", "isHandlerEnabled", "onEnabledChange", "registerListeners", "unregisterListeners", "onBegin", "onActivate", "onEnd", "onCancel", "onFail", "destroy", "_view", "value"], "sourceRoot": "../../../../src", "sources": ["web/tools/GestureHandlerWebDelegate.ts"], "mappings": ";;AAAA,OAAOA,cAAc,MAAM,sBAAsB;AAMjD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,iBAAiB,QAAQ,UAAU;AAG5C,SAASC,WAAW,QAAQ,qCAAqC;AACjE,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,qBAAqB;AAOnD,OAAO,MAAMC,yBAAyB,CAEtC;EACUC,aAAa,GAAG,KAAK;EAIrBC,aAAa,GAA4B,EAAE;EAC3CC,iBAAiB,GAAsB;IAC7CC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE;EACf,CAAC;EAEDC,IAAIA,CAACC,OAAe,EAAEC,OAAwB,EAAQ;IACpD,IAAI,CAACD,OAAO,EAAE;MACZ,MAAM,IAAIE,KAAK,CACb,wCAAwCD,OAAO,CAACE,UAAU,EAC5D,CAAC;IACH;IAEA,IAAI,CAACT,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACU,cAAc,GAAGH,OAAO;IAC7B,IAAI,CAACI,IAAI,GAAGnB,cAAc,CAACc,OAAO,CAA2B;IAE7D,IAAI,CAACJ,iBAAiB,GAAG;MACvBC,UAAU,EAAE,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,UAAU;MACtCC,WAAW,EAAE,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR;IAC/B,CAAC;IAED,MAAMS,MAAM,GAAGN,OAAO,CAACM,MAAM;IAE7B,IAAI,CAACC,aAAa,CAACD,MAAM,CAACE,OAAO,CAAC;IAClC,IAAI,CAACC,cAAc,CAACH,MAAM,CAACE,OAAO,CAAC;IACnC,IAAI,CAACE,cAAc,CAACJ,MAAM,CAACE,OAAO,CAAC;IAEnC,IAAI,CAACd,aAAa,CAACiB,IAAI,CAAC,IAAIzB,mBAAmB,CAAC,IAAI,CAACkB,IAAI,CAAC,CAAC;IAC3D,IAAI,CAACV,aAAa,CAACiB,IAAI,CAAC,IAAIrB,oBAAoB,CAAC,IAAI,CAACc,IAAI,CAAC,CAAC;IAC5D,IAAI,CAACV,aAAa,CAACiB,IAAI,CAAC,IAAIpB,iBAAiB,CAAC,IAAI,CAACa,IAAI,CAAC,CAAC;IAEzD,IAAI,CAACV,aAAa,CAACkB,OAAO,CAAEC,OAAO,IACjC,IAAI,CAACV,cAAc,CAACW,kBAAkB,CAACD,OAAO,CAChD,CAAC;EACH;EAEAzB,iBAAiBA,CAAC;IAAE2B,CAAC;IAAEC;EAA4B,CAAC,EAAW;IAC7D,OAAO5B,iBAAiB,CAAC,IAAI,CAACgB,IAAI,EAAE;MAAEW,CAAC;MAAEC;IAAE,CAAC,CAAC;EAC/C;EAEAC,WAAWA,CAAA,EAAkB;IAC3B,MAAMC,IAAI,GAAG,IAAI,CAACd,IAAI,CAACe,qBAAqB,CAAC,CAAC;IAE9C,OAAO;MACLC,KAAK,EAAEF,IAAI,CAACG,IAAI;MAChBC,KAAK,EAAEJ,IAAI,CAACK,GAAG;MACfC,KAAK,EAAEN,IAAI,CAACM,KAAK;MACjBC,MAAM,EAAEP,IAAI,CAACO;IACf,CAAC;EACH;EAEAC,KAAKA,CAAA,EAAS;IACZ,IAAI,CAAChC,aAAa,CAACkB,OAAO,CAAEC,OAA8B,IACxDA,OAAO,CAACc,YAAY,CAAC,CACvB,CAAC;EACH;EAEAC,cAAcA,CAAA,EAAG;IACf,MAAMtB,MAAM,GAAG,IAAI,CAACH,cAAc,CAACG,MAAM;IAEzC,IACEA,MAAM,CAACuB,YAAY,IACnBvB,MAAM,CAACuB,YAAY,KAAK,MAAM,IAC9B,IAAI,CAAC1B,cAAc,CAAC2B,KAAK,KAAK3C,KAAK,CAAC4C,MAAM,EAC1C;MACA,IAAI,CAAC3B,IAAI,CAACC,KAAK,CAAC2B,MAAM,GAAG,MAAM;IACjC;EACF;EAEQC,wBAAwBA,CAAC3B,MAAc,EAAE;IAC/C,OACGA,MAAM,CAAC4B,iBAAiB,KAAKC,SAAS,IACrC,IAAI,CAAChC,cAAc,CAACiC,gBAAgB,CAAC/C,WAAW,CAACgD,KAAK,CAAC,IACzD/B,MAAM,CAAC4B,iBAAiB,KAAK,KAAK;EAEtC;EAEQI,uBAAuBA,CAAChC,MAAc,EAAQ;IACpD,IAAI,IAAI,CAAC2B,wBAAwB,CAAC3B,MAAM,CAAC,EAAE;MACzC,IAAI,CAACF,IAAI,CAACmC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACC,kBAAkB,CAAC;IACpE,CAAC,MAAM,IAAIlC,MAAM,CAAC4B,iBAAiB,EAAE;MACnC,IAAI,CAAC9B,IAAI,CAACmC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACL,iBAAiB,CAAC;IACnE;EACF;EAEQO,0BAA0BA,CAACnC,MAAc,EAAQ;IACvD,IAAI,IAAI,CAAC2B,wBAAwB,CAAC3B,MAAM,CAAC,EAAE;MACzC,IAAI,CAACF,IAAI,CAACsC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACF,kBAAkB,CAAC;IACvE,CAAC,MAAM,IAAIlC,MAAM,CAAC4B,iBAAiB,EAAE;MACnC,IAAI,CAAC9B,IAAI,CAACsC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACR,iBAAiB,CAAC;IACtE;EACF;EAEQM,kBAAkBA,CAAaG,CAAa,EAAQ;IAC1DA,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB;EAEQV,iBAAiBA,CAAaS,CAAa,EAAQ;IACzDA,CAAC,CAACE,eAAe,CAAC,CAAC;EACrB;EAEQtC,aAAaA,CAACuC,gBAAyB,EAAE;IAC/C,MAAM;MAAElD;IAAW,CAAC,GAAG,IAAI,CAACO,cAAc,CAACG,MAAM;IAEjD,IAAI,CAACF,IAAI,CAACC,KAAK,CAAC,YAAY,CAAC,GAAGyC,gBAAgB,GAC3ClD,UAAU,IAAI,MAAM,GACrB,IAAI,CAACD,iBAAiB,CAACC,UAAU;IAErC,IAAI,CAACQ,IAAI,CAACC,KAAK,CAAC,kBAAkB,CAAC,GAAGyC,gBAAgB,GACjDlD,UAAU,IAAI,MAAM,GACrB,IAAI,CAACD,iBAAiB,CAACC,UAAU;EACvC;EAEQa,cAAcA,CAACqC,gBAAyB,EAAE;IAChD,MAAM;MAAEjD;IAAY,CAAC,GAAG,IAAI,CAACM,cAAc,CAACG,MAAM;IAElD,IAAI,CAACF,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,GAAGyC,gBAAgB,GAC5CjD,WAAW,IAAI,MAAM,GACtB,IAAI,CAACF,iBAAiB,CAACE,WAAW;;IAEtC;IACA,IAAI,CAACO,IAAI,CAACC,KAAK,CAAC,oBAAoB,CAAC,GAAGyC,gBAAgB,GACnDjD,WAAW,IAAI,MAAM,GACtB,IAAI,CAACF,iBAAiB,CAACE,WAAW;EACxC;EAEQa,cAAcA,CAACoC,gBAAyB,EAAE;IAChD,MAAMxC,MAAM,GAAG,IAAI,CAACH,cAAc,CAACG,MAAM;IAEzC,IAAIwC,gBAAgB,EAAE;MACpB,IAAI,CAACR,uBAAuB,CAAChC,MAAM,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACmC,0BAA0B,CAACnC,MAAM,CAAC;IACzC;EACF;EAEAyC,eAAeA,CAACvC,OAAgB,EAAQ;IACtC,IAAI,CAAC,IAAI,CAACf,aAAa,EAAE;MACvB;IACF;IAEA,IAAI,CAACc,aAAa,CAACC,OAAO,CAAC;IAC3B,IAAI,CAACC,cAAc,CAACD,OAAO,CAAC;IAC5B,IAAI,CAACE,cAAc,CAACF,OAAO,CAAC;IAE5B,IAAIA,OAAO,EAAE;MACX,IAAI,CAACd,aAAa,CAACkB,OAAO,CAAEC,OAAO,IAAK;QACtC;QACA;QACA;QACA;QACA;QACAA,OAAO,CAACmC,iBAAiB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACtD,aAAa,CAACkB,OAAO,CAAEC,OAAO,IAAK;QACtCA,OAAO,CAACoC,mBAAmB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF;EAEAC,OAAOA,CAAA,EAAS;IACd;EAAA;EAGFC,UAAUA,CAAA,EAAS;IACjB,MAAM7C,MAAM,GAAG,IAAI,CAACH,cAAc,CAACG,MAAM;IAEzC,IACE,CAAC,CAAC,IAAI,CAACF,IAAI,CAACC,KAAK,CAAC2B,MAAM,IAAI,IAAI,CAAC5B,IAAI,CAACC,KAAK,CAAC2B,MAAM,KAAK,MAAM,KAC7D1B,MAAM,CAACuB,YAAY,EACnB;MACA,IAAI,CAACzB,IAAI,CAACC,KAAK,CAAC2B,MAAM,GAAG1B,MAAM,CAACuB,YAAY;IAC9C;EACF;EAEAuB,KAAKA,CAAA,EAAS;IACZ,IAAI,CAACxB,cAAc,CAAC,CAAC;EACvB;EAEAyB,QAAQA,CAAA,EAAS;IACf,IAAI,CAACzB,cAAc,CAAC,CAAC;EACvB;EAEA0B,MAAMA,CAAA,EAAS;IACb,IAAI,CAAC1B,cAAc,CAAC,CAAC;EACvB;EAEO2B,OAAOA,CAACjD,MAAc,EAAQ;IACnC,IAAI,CAACmC,0BAA0B,CAACnC,MAAM,CAAC;IAEvC,IAAI,CAACZ,aAAa,CAACkB,OAAO,CAAEC,OAAO,IAAK;MACtCA,OAAO,CAACoC,mBAAmB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEA,IAAW7C,IAAIA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACoD,KAAK;EACnB;EACA,IAAWpD,IAAIA,CAACqD,KAAkB,EAAE;IAClC,IAAI,CAACD,KAAK,GAAGC,KAAK;EACpB;AACF", "ignoreList": []}