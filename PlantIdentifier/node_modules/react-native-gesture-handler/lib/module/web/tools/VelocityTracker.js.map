{"version": 3, "names": ["Circular<PERSON><PERSON>er", "LeastSquareSolver", "VelocityTracker", "assumePointerMoveStoppedMilliseconds", "historySize", "horizonMilliseconds", "minSampleSize", "constructor", "samples", "add", "event", "push", "getVelocityEstimate", "x", "y", "w", "time", "sampleCount", "index", "size", "newestSample", "get", "previousSample", "sample", "age", "delta", "Math", "abs", "xSolver", "xFit", "solve", "ySolver", "yFit", "xVelocity", "coefficients", "yVelocity", "velocity", "estimate", "reset", "clear"], "sourceRoot": "../../../../src", "sources": ["web/tools/VelocityTracker.ts"], "mappings": ";;AACA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,eAAe,MAAMC,eAAe,CAAC;EAC3BC,oCAAoC,GAAG,EAAE;EACzCC,WAAW,GAAG,EAAE;EAChBC,mBAAmB,GAAG,GAAG;EACzBC,aAAa,GAAG,CAAC;EAIzBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,IAAIR,cAAc,CAAe,IAAI,CAACI,WAAW,CAAC;EACnE;EAEOK,GAAGA,CAACC,KAAmB,EAAQ;IACpC,IAAI,CAACF,OAAO,CAACG,IAAI,CAACD,KAAK,CAAC;EAC1B;;EAEA;EACA;EACA;EACA;EACA;EACA;EACQE,mBAAmBA,CAAA,EAA4B;IACrD,MAAMC,CAAC,GAAG,EAAE;IACZ,MAAMC,CAAC,GAAG,EAAE;IACZ,MAAMC,CAAC,GAAG,EAAE;IACZ,MAAMC,IAAI,GAAG,EAAE;IAEf,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,KAAK,GAAG,IAAI,CAACV,OAAO,CAACW,IAAI,GAAG,CAAC;IACjC,MAAMC,YAAY,GAAG,IAAI,CAACZ,OAAO,CAACa,GAAG,CAACH,KAAK,CAAC;IAC5C,IAAI,CAACE,YAAY,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAIE,cAAc,GAAGF,YAAY;;IAEjC;IACA;IACA,OAAOH,WAAW,GAAG,IAAI,CAACT,OAAO,CAACW,IAAI,EAAE;MACtC,MAAMI,MAAM,GAAG,IAAI,CAACf,OAAO,CAACa,GAAG,CAACH,KAAK,CAAC;MAEtC,MAAMM,GAAG,GAAGJ,YAAY,CAACJ,IAAI,GAAGO,MAAM,CAACP,IAAI;MAC3C,MAAMS,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAACP,IAAI,GAAGM,cAAc,CAACN,IAAI,CAAC;MACzDM,cAAc,GAAGC,MAAM;MAEvB,IACEC,GAAG,GAAG,IAAI,CAACnB,mBAAmB,IAC9BoB,KAAK,GAAG,IAAI,CAACtB,oCAAoC,EACjD;QACA;MACF;MAEAU,CAAC,CAACF,IAAI,CAACY,MAAM,CAACV,CAAC,CAAC;MAChBC,CAAC,CAACH,IAAI,CAACY,MAAM,CAACT,CAAC,CAAC;MAChBC,CAAC,CAACJ,IAAI,CAAC,CAAC,CAAC;MACTK,IAAI,CAACL,IAAI,CAAC,CAACa,GAAG,CAAC;MAEfP,WAAW,EAAE;MACbC,KAAK,EAAE;IACT;IAEA,IAAID,WAAW,IAAI,IAAI,CAACX,aAAa,EAAE;MACrC,MAAMsB,OAAO,GAAG,IAAI3B,iBAAiB,CAACe,IAAI,EAAEH,CAAC,EAAEE,CAAC,CAAC;MACjD,MAAMc,IAAI,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC;MAE7B,IAAID,IAAI,KAAK,IAAI,EAAE;QACjB,MAAME,OAAO,GAAG,IAAI9B,iBAAiB,CAACe,IAAI,EAAEF,CAAC,EAAEC,CAAC,CAAC;QACjD,MAAMiB,IAAI,GAAGD,OAAO,CAACD,KAAK,CAAC,CAAC,CAAC;QAE7B,IAAIE,IAAI,KAAK,IAAI,EAAE;UACjB,MAAMC,SAAS,GAAGJ,IAAI,CAACK,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;UAC7C,MAAMC,SAAS,GAAGH,IAAI,CAACE,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;UAE7C,OAAO,CAACD,SAAS,EAAEE,SAAS,CAAC;QAC/B;MACF;IACF;IAEA,OAAO,IAAI;EACb;EAEA,IAAWC,QAAQA,CAAA,EAAqB;IACtC,MAAMC,QAAQ,GAAG,IAAI,CAACzB,mBAAmB,CAAC,CAAC;IAC3C,IAAIyB,QAAQ,KAAK,IAAI,EAAE;MACrB,OAAOA,QAAQ;IACjB;IACA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACf;EAEOC,KAAKA,CAAA,EAAS;IACnB,IAAI,CAAC9B,OAAO,CAAC+B,KAAK,CAAC,CAAC;EACtB;AACF", "ignoreList": []}