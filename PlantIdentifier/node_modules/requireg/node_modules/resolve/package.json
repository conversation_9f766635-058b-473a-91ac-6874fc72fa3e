{"name": "resolve", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "version": "1.7.1", "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "main": "index.js", "keywords": ["resolve", "require", "node", "module"], "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "tests-only": "tape test/*.js", "pretest": "npm run lint", "test": "npm run --silent tests-only"}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "eslint": "^4.19.1", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "tap": "0.4.13", "tape": "^4.9.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "dependencies": {"path-parse": "^1.0.5"}}