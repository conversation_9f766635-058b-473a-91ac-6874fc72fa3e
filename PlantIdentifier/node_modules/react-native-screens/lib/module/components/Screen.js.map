{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "Animated", "Platform", "TransitionProgressContext", "DelayedFreeze", "freezeEnabled", "isNativePlatformSupported", "screensEnabled", "ScreenNativeComponent", "ModalScreenNativeComponent", "usePrevious", "EDGE_TO_EDGE", "transformEdgeToEdgeProps", "SHEET_DIMMED_ALWAYS", "resolveSheetAllowedDetents", "resolveSheetInitialDetentIndex", "resolveSheetLargestUndimmedDetent", "AnimatedNativeScreen", "createAnimatedComponent", "AnimatedNativeModalScreen", "InnerScreen", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "freezeOnBlur", "shouldFreeze", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "screenId", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "resolvedSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "shouldUseModalScreenComponent", "select", "ios", "undefined", "android", "default", "AnimatedScreen", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "console", "warn", "Error", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "freeze", "createElement", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "value", "View", "ScreenContext", "createContext", "Screen", "ScreenWrapper", "useContext", "displayName"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAQC,QAAQ,QAAQ,cAAc;AAEvD,OAAOC,yBAAyB,MAAM,8BAA8B;AACpE,OAAOC,aAAa,MAAM,yBAAyB;AAGnD,SACEC,aAAa,EACbC,yBAAyB,EACzBC,cAAc,QACT,SAAS;;AAEhB;AACA,OAAOC,qBAAqB,MAErB,iCAAiC;AACxC,OAAOC,0BAA0B,MAE1B,sCAAsC;AAE7C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,YAAY,EAAEC,wBAAwB,QAAQ,wBAAwB;AAC/E,SACEC,mBAAmB,EACnBC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,iCAAiC,QAC5B,iBAAiB;AAGxB,MAAMC,oBAAoB,GAAGhB,QAAQ,CAACiB,uBAAuB,CAC3DV,qBACF,CAAC;AACD,MAAMW,yBAAyB,GAAGlB,QAAQ,CAACiB,uBAAuB,CAChET,0BACF,CAAC;;AAED;AACA;;AAkBA,OAAO,MAAMW,WAAW,gBAAGpB,KAAK,CAACqB,UAAU,CACzC,SAASD,WAAWA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAGxB,KAAK,CAACyB,MAAM,CAAoB,IAAI,CAAC;EACtDzB,KAAK,CAAC0B,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAGlB,WAAW,CAACY,KAAK,CAACO,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIP,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACS,cAAc,GAAGR,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMS,OAAO,GAAGhC,KAAK,CAACyB,MAAM,CAAC,IAAIxB,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC3D,MAAMO,QAAQ,GAAGlC,KAAK,CAACyB,MAAM,CAAC,IAAIxB,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC5D,MAAMQ,YAAY,GAAGnC,KAAK,CAACyB,MAAM,CAAC,IAAIxB,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAEhE,MAAM;IACJS,OAAO,GAAG7B,cAAc,CAAC,CAAC;IAC1B8B,YAAY,GAAGhC,aAAa,CAAC,CAAC;IAC9BiC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGjB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAkB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAG5B,mBAAmB;IACrD6B,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,QAAQ;IACRC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGb,IAAI;EAER,IAAIH,OAAO,IAAI9B,yBAAyB,EAAE;IACxC,MAAM+C,2BAA2B,GAC/BvC,0BAA0B,CAAC0B,mBAAmB,CAAC;IACjD,MAAMc,kCAAkC,GACtCtC,iCAAiC,CAC/ByB,+BAA+B,EAC/BY,2BAA2B,CAAC3D,MAAM,GAAG,CACvC,CAAC;IACH,MAAM6D,+BAA+B,GAAGxC,8BAA8B,CACpE+B,uBAAuB,EACvBO,2BAA2B,CAAC3D,MAAM,GAAG,CACvC,CAAC;;IAED;IACA;IACA,MAAM8D,6BAA6B,GAAGtD,QAAQ,CAACuD,MAAM,CAAC;MACpDC,GAAG,EAAE,EACHV,iBAAiB,KAAKW,SAAS,IAC/BX,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,CAClD;MACDY,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAMC,cAAc,GAAGN,6BAA6B,GAChDrC,yBAAyB,GACzBF,oBAAoB;IAExB,IAAI;MACF;MACA;MACA;MACA8C,MAAM;MACNlC,aAAa;MACbmC,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAG9C;IACL,CAAC,GAAGiB,IAAI;IAER,IAAIwB,MAAM,KAAKJ,SAAS,IAAI9B,aAAa,KAAK8B,SAAS,EAAE;MACvDU,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACDzC,aAAa,GAAGkC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;IAEA,IACEE,aAAa,IACbrC,iBAAiB,KAAK+B,SAAS,IAC/B9B,aAAa,KAAK8B,SAAS,EAC3B;MACA,IAAI/B,iBAAiB,GAAGC,aAAa,EAAE;QACrC,MAAM,IAAI0C,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAMC,SAAS,GAAIjD,GAAe,IAAK;MACrC;MACA;MACA,IAAIA,GAAG,EAAEkD,UAAU,EAAEC,eAAe,EAAEN,KAAK,EAAE;QAC3C7C,GAAG,CAACkD,UAAU,CAACC,eAAe,CAACN,KAAK,GAAG;UACrC,GAAG7C,GAAG,CAACkD,UAAU,CAACC,eAAe,CAACN,KAAK;UACvCO,OAAO,EAAE;QACX,CAAC;QACD7C,MAAM,CAACP,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAEqD,WAAW,EAAEF,eAAe,EAAEN,KAAK,EAAE;QACnD7C,GAAG,CAACqD,WAAW,CAACF,eAAe,CAACN,KAAK,GAAG;UACtC,GAAG7C,GAAG,CAACqD,WAAW,CAACF,eAAe,CAACN,KAAK;UACxCO,OAAO,EAAE;QACX,CAAC;QACD7C,MAAM,CAACP,GAAG,CAAC;MACb;IACF,CAAC;IAED,MAAMsD,MAAM,GACVxC,YAAY,KACXC,YAAY,KAAKqB,SAAS,GAAGrB,YAAY,GAAGT,aAAa,KAAK,CAAC,CAAC;IAEnE,oBACE7B,KAAA,CAAA8E,aAAA,CAAC1E,aAAa;MAACyE,MAAM,EAAEA;IAAO,gBAC5B7E,KAAA,CAAA8E,aAAA,CAAChB,cAAc,EAAA3E,QAAA,KACTmC,KAAK;MACT;AACZ;AACA;AACA;AACA;MACY2B,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEe,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEW,MAAM,EAAEpB;MAAU,CAAC,CAAE;MACtC9B,aAAa,EAAEA,aAAc;MAC7BkB,QAAQ,EAAEA,QAAS;MACnBP,mBAAmB,EAAEa,2BAA4B;MACjD2B,0BAA0B,EAAE1B,kCAAmC;MAC/DT,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/DqC,kBAAkB,EAAE1B,+BAAgC;MACpDW,uBAAuB,EAAE;QACvBgB,KAAK,EAAEhB,uBAAuB,EAAEgB,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEjB,uBAAuB,EAAEiB,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAElB,uBAAuB,EAAEkB,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEnB,uBAAuB,EAAEmB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACA9D,GAAG,EAAEiD,SAAU;MACfc,oBAAoB,EAClB,CAACrB,aAAa,GACVN,SAAS,GACT1D,QAAQ,CAACsF,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACXtD,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAEsD,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACxB,aAAa;IAAK;IAClBD,QAAQ,gBAERhE,KAAA,CAAA8E,aAAA,CAAC3E,yBAAyB,CAACuF,QAAQ;MACjCC,KAAK,EAAE;QACLzD,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACD6B,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACNlC,aAAa;MACbuC,KAAK;MACL;MACArC,cAAc;MACd,GAAGT;IACL,CAAC,GAAGiB,IAAI;IAER,IAAIwB,MAAM,KAAKJ,SAAS,IAAI9B,aAAa,KAAK8B,SAAS,EAAE;MACvD9B,aAAa,GAAGkC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACE/D,KAAA,CAAA8E,aAAA,CAAC7E,QAAQ,CAAC2F,IAAI,EAAAzG,QAAA;MACZiF,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEO,OAAO,EAAE9C,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEN,GAAG,EAAEO;IAAO,GACRR,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACA,OAAO,MAAMuE,aAAa,gBAAG7F,KAAK,CAAC8F,aAAa,CAAC1E,WAAW,CAAC;AAE7D,MAAM2E,MAAM,gBAAG/F,KAAK,CAACqB,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAMyE,aAAa,GAAGhG,KAAK,CAACiG,UAAU,CAACJ,aAAa,CAAC,IAAIzE,WAAW;EAEpE,oBACEpB,KAAA,CAAA8E,aAAA,CAACkB,aAAa,EAAA7G,QAAA,KACPwB,YAAY,GAAGC,wBAAwB,CAACU,KAAK,CAAC,GAAGA,KAAK;IAC3DC,GAAG,EAAEA;EAAI,EACV,CAAC;AAEN,CAAC,CAAC;AAEFwE,MAAM,CAACG,WAAW,GAAG,QAAQ;AAE7B,eAAeH,MAAM", "ignoreList": []}