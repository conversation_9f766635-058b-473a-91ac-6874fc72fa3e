import React from 'react';
import type { ViewProps } from 'react-native';
import type { NativeProps } from '../../fabric/gamma/SplitViewScreenNativeComponent';
export type SplitViewScreenNativeProps = NativeProps & {};
type SplitViewScreenProps = {
    children?: ViewProps['children'];
} & SplitViewScreenNativeProps;
/**
 * EXPERIMENTAL API, MIGHT CHANGE W/O ANY NOTICE
 */
declare function SplitViewScreen({ children }: SplitViewScreenProps): React.JSX.Element;
export default SplitViewScreen;
//# sourceMappingURL=SplitViewScreen.d.ts.map