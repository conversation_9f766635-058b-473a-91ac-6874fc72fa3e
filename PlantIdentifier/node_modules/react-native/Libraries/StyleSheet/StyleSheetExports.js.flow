/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

import type {____Styles_Internal} from './StyleSheetTypes';

import composeStyles from '../../src/private/styles/composeStyles';
import flattenStyle from './flattenStyle';

/**
 * This is defined as the width of a thin line on the platform. It can be
 * used as the thickness of a border or division between two elements.
 * Example:
 * ```
 *   {
 *     borderBottomColor: '#bbb',
 *     borderBottomWidth: StyleSheet.hairlineWidth
 *   }
 * ```
 *
 * This constant will always be a round number of pixels (so a line defined
 * by it look crisp) and will try to match the standard width of a thin line
 * on the underlying platform. However, you should not rely on it being a
 * constant size, because on different platforms and screen densities its
 * value may be calculated differently.
 */
declare export const hairlineWidth: number;

/**
 * A very common pattern is to create overlays with position absolute and zero positioning,
 * so `absoluteFill` can be used for convenience and to reduce duplication of these repeated
 * styles.
 */
declare export const absoluteFill: any;

/**
 * Sometimes you may want `absoluteFill` but with a couple tweaks - `absoluteFillObject` can be
 * used to create a customized entry in a `StyleSheet`, e.g.:
 *
 *   const styles = StyleSheet.create({
 *     wrapper: {
 *       ...StyleSheet.absoluteFillObject,
 *       top: 10,
 *       backgroundColor: 'transparent',
 *     },
 *   });
 */
declare export const absoluteFillObject: {
  +bottom: 0,
  +left: 0,
  +position: 'absolute',
  +right: 0,
  +top: 0,
};

/**
 * Combines two styles such that style2 will override any styles in style1.
 * If either style is falsy, the other one is returned without allocating
 * an array, saving allocations and maintaining reference equality for
 * PureComponent checks.
 */
declare export const compose: typeof composeStyles;

/**
 * Flattens an array of style objects, into one aggregated style object.
 *
 * Example:
 * ```
 * const styles = StyleSheet.create({
 *   listItem: {
 *     flex: 1,
 *     fontSize: 16,
 *     color: 'white'
 *   },
 *   selectedListItem: {
 *     color: 'green'
 *   }
 * });
 *
 * StyleSheet.flatten([styles.listItem, styles.selectedListItem])
 * // returns { flex: 1, fontSize: 16, color: 'green' }
 * ```
 */
declare export const flatten: typeof flattenStyle;

/**
 * WARNING: EXPERIMENTAL. Breaking changes will probably happen a lot and will
 * not be reliably announced. The whole thing might be deleted, who knows? Use
 * at your own risk.
 *
 * Sets a function to use to pre-process a style property value. This is used
 * internally to process color and transform values. You should not use this
 * unless you really know what you are doing and have exhausted other options.
 */
declare export const setStyleAttributePreprocessor: (
  property: string,
  process: (nextProp: any) => any,
) => void;

/**
 * An identity function for creating style sheets.
 */
// $FlowFixMe[unsupported-variance-annotation]
declare export const create: <+S: ____Styles_Internal>(
  obj: S & ____Styles_Internal,
) => $ReadOnly<S>;
