/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<a20a6d477d138124b7a60993ccd38227>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/useAnimatedValue.js
 */

import Animated from "./Animated";
declare function useAnimatedValue(initialValue: number, config?: null | undefined | Animated.AnimatedConfig): Animated.Value;
export default useAnimatedValue;
