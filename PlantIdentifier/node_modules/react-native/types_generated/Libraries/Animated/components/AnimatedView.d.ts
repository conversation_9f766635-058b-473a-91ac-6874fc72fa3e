/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<956cae49df532550d2b45e901e8a1b65>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/components/AnimatedView.js
 */

import type { AnimatedComponentType } from "../createAnimatedComponent";
import View from "../../Components/View/View";
import * as React from "react";
declare const $$AnimatedView: AnimatedComponentType<React.JSX.LibraryManagedAttributes<typeof View, React.ComponentProps<typeof View>>, React.ComponentRef<typeof View>>;
declare type $$AnimatedView = typeof $$AnimatedView;
export default $$AnimatedView;
