/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<edba94538347a4e4b274bf83b307e81b>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/Platform.js.flow
 */

import type { Platform } from "./PlatformTypes";
declare const $$Platform: Platform;
declare type $$Platform = typeof $$Platform;
export default $$Platform;
