/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<bb661e1857a049700aac2535e54d821f>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/DeviceInfo.js
 */

export type { DeviceInfoConstants } from "./NativeDeviceInfo";
import NativeDeviceInfo from "./NativeDeviceInfo";
declare const $$DeviceInfo: typeof NativeDeviceInfo;
declare type $$DeviceInfo = typeof $$DeviceInfo;
export default $$DeviceInfo;
