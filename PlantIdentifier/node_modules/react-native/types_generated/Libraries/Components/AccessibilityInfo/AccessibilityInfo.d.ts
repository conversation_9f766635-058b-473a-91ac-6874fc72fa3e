/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<56d3f5143e468a4bb405c333f8f6a892>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/AccessibilityInfo/AccessibilityInfo.js
 */

import type { HostInstance } from "../../../src/private/types/HostInstance";
import type { EventSubscription } from "../../vendor/emitter/EventEmitter";
type AccessibilityEventDefinitionsAndroid = {
  accessibilityServiceChanged: [boolean];
  highTextContrastChanged: [boolean];
};
type AccessibilityEventDefinitionsIOS = {
  announcementFinished: [{
    announcement: string;
    success: boolean;
  }];
  boldTextChanged: [boolean];
  grayscaleChanged: [boolean];
  invertColorsChanged: [boolean];
  reduceTransparencyChanged: [boolean];
  darkerSystemColorsChanged: [boolean];
};
type AccessibilityEventDefinitions = Omit<AccessibilityEventDefinitionsAndroid, keyof AccessibilityEventDefinitionsIOS | keyof {
  change: [boolean];
  reduceMotionChanged: [boolean];
  screenReaderChanged: [boolean];
}> & Omit<AccessibilityEventDefinitionsIOS, keyof {
  change: [boolean];
  reduceMotionChanged: [boolean];
  screenReaderChanged: [boolean];
}> & {
  change: [boolean];
  reduceMotionChanged: [boolean];
  screenReaderChanged: [boolean];
};
type AccessibilityEventTypes = "click" | "focus" | "viewHoverEnter";
declare const AccessibilityInfo: {
  /**
   * Query whether bold text is currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when bold text is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#isBoldTextEnabled
   */
  isBoldTextEnabled(): Promise<boolean>;
  /**
   * Query whether grayscale is currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when grayscale is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#isGrayscaleEnabled
   */
  isGrayscaleEnabled(): Promise<boolean>;
  /**
   * Query whether inverted colors are currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when invert color is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#isInvertColorsEnabled
   */
  isInvertColorsEnabled(): Promise<boolean>;
  /**
   * Query whether reduced motion is currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when a reduce motion is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#isReduceMotionEnabled
   */
  isReduceMotionEnabled(): Promise<boolean>;
  /**
   * Query whether high text contrast is currently enabled. Android only.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when high text contrast is enabled and `false` otherwise.
   */
  isHighTextContrastEnabled(): Promise<boolean>;
  /**
   * Query whether dark system colors is currently enabled. iOS only.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when dark system colors is enabled and `false` otherwise.
   */
  isDarkerSystemColorsEnabled(): Promise<boolean>;
  /**
   * Query whether reduce motion and prefer cross-fade transitions settings are currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when  prefer cross-fade transitions is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#prefersCrossFadeTransitions
   */
  prefersCrossFadeTransitions(): Promise<boolean>;
  /**
   * Query whether reduced transparency is currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when a reduce transparency is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#isReduceTransparencyEnabled
   */
  isReduceTransparencyEnabled(): Promise<boolean>;
  /**
   * Query whether a screen reader is currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when a screen reader is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#isScreenReaderEnabled
   */
  isScreenReaderEnabled(): Promise<boolean>;
  /**
   * Query whether Accessibility Service is currently enabled.
   *
   * Returns a promise which resolves to a boolean.
   * The result is `true` when any service is enabled and `false` otherwise.
   *
   * @platform android
   *
   * See https://reactnative.dev/docs/accessibilityinfo/#isaccessibilityserviceenabled-android
   */
  isAccessibilityServiceEnabled(): Promise<boolean>;
  /**
   * Add an event handler. Supported events:
   *
   * - `reduceMotionChanged`: Fires when the state of the reduce motion toggle changes.
   *   The argument to the event handler is a boolean. The boolean is `true` when a reduce
   *   motion is enabled (or when "Transition Animation Scale" in "Developer options" is
   *   "Animation off") and `false` otherwise.
   * - `screenReaderChanged`: Fires when the state of the screen reader changes. The argument
   *   to the event handler is a boolean. The boolean is `true` when a screen
   *   reader is enabled and `false` otherwise.
   *
   * These events are only supported on iOS:
   *
   * - `boldTextChanged`: iOS-only event. Fires when the state of the bold text toggle changes.
   *   The argument to the event handler is a boolean. The boolean is `true` when a bold text
   *   is enabled and `false` otherwise.
   * - `grayscaleChanged`: iOS-only event. Fires when the state of the gray scale toggle changes.
   *   The argument to the event handler is a boolean. The boolean is `true` when a gray scale
   *   is enabled and `false` otherwise.
   * - `invertColorsChanged`: iOS-only event. Fires when the state of the invert colors toggle
   *   changes. The argument to the event handler is a boolean. The boolean is `true` when a invert
   *   colors is enabled and `false` otherwise.
   * - `reduceTransparencyChanged`: iOS-only event. Fires when the state of the reduce transparency
   *   toggle changes.  The argument to the event handler is a boolean. The boolean is `true`
   *   when a reduce transparency is enabled and `false` otherwise.
   * - `announcementFinished`: iOS-only event. Fires when the screen reader has
   *   finished making an announcement. The argument to the event handler is a
   *   dictionary with these keys:
   *     - `announcement`: The string announced by the screen reader.
   *     - `success`: A boolean indicating whether the announcement was
   *       successfully made.
   * - `darkerSystemColorsChanged`: iOS-only event. Fires when the state of the dark system colors
   *   toggle changes. The argument to the event handler is a boolean. The boolean is `true` when
   *   dark system colors is enabled and `false` otherwise.
   *
   * These events are only supported on Android:
   *
   * - `highTextContrastChanged`: Android-only event. Fires when the state of the high text contrast
   *   toggle changes. The argument to the event handler is a boolean. The boolean is `true` when
   *   high text contrast is enabled and `false` otherwise.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#addeventlistener
   */
  addEventListener<K extends keyof AccessibilityEventDefinitions>(eventName: K, handler: (...$$REST$$: AccessibilityEventDefinitions[K]) => void): EventSubscription;
  /**
   * Set accessibility focus to a React component.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#setaccessibilityfocus
   */
  setAccessibilityFocus(reactTag: number): void;
  /**
   * Send a named accessibility event to a HostComponent.
   */
  sendAccessibilityEvent(handle: HostInstance, eventType: AccessibilityEventTypes): void;
  /**
   * Post a string to be announced by the screen reader.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#announceforaccessibility
   */
  announceForAccessibility(announcement: string): void;
  /**
   * Post a string to be announced by the screen reader.
   * - `announcement`: The string announced by the screen reader.
   * - `options`: An object that configures the reading options.
   *   - `queue`: The announcement will be queued behind existing announcements. iOS only.
   */
  announceForAccessibilityWithOptions(announcement: string, options: {
    queue?: boolean;
  }): void;
  /**
   * Get the recommended timeout for changes to the UI needed by this user.
   *
   * See https://reactnative.dev/docs/accessibilityinfo#getrecommendedtimeoutmillis
   */
  getRecommendedTimeoutMillis(originalTimeout: number): Promise<number>;
};
/**
 * Sometimes it's useful to know whether or not the device has a screen reader
 * that is currently active. The `AccessibilityInfo` API is designed for this
 * purpose. You can use it to query the current state of the screen reader as
 * well as to register to be notified when the state of the screen reader
 * changes.
 *
 * See https://reactnative.dev/docs/accessibilityinfo
 */
declare const $$AccessibilityInfo: typeof AccessibilityInfo;
declare type $$AccessibilityInfo = typeof $$AccessibilityInfo;
export default $$AccessibilityInfo;
