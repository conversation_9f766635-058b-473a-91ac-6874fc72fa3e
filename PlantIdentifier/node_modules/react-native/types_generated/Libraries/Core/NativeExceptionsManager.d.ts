/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<02d3e1b858f6f1799475a9ddf556cad2>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Core/NativeExceptionsManager.js
 */

export * from "../../src/private/specs_DEPRECATED/modules/NativeExceptionsManager";
import NativeExceptionsManager from "../../src/private/specs_DEPRECATED/modules/NativeExceptionsManager";
declare const $$NativeExceptionsManager: typeof NativeExceptionsManager;
declare type $$NativeExceptionsManager = typeof $$NativeExceptionsManager;
export default $$NativeExceptionsManager;
