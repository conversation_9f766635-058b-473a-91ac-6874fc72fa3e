/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<70ea1624bbf0388c8b7ecf25c096f982>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/StyleSheet/processColor.js
 */

import type { ColorValue, NativeColorValue } from "./StyleSheet";
export type ProcessedColorValue = number | NativeColorValue;
declare function processColor(color?: null | undefined | (number | ColorValue)): null | undefined | ProcessedColorValue;
declare const $$processColor: typeof processColor;
declare type $$processColor = typeof $$processColor;
export default $$processColor;
