{"version": 3, "file": "getNodeModuleName.js", "sourceRoot": "", "sources": ["../../src/utils/getNodeModuleName.ts"], "names": [], "mappings": ";;AAcA,8CASC;AAvBD,SAAS,kBAAkB,CAAC,UAAkB;IAC5C,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/B,MAAM,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IACD,MAAM,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC5C,OAAO,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC;AAChD,CAAC;AAED,MAAM,mBAAmB,GAAG,sDAAsD,CAAC;AAEnF,SAAgB,iBAAiB,CAAC,QAAgB;IAChD,wGAAwG;IACxG,+KAA+K;IAC/K,uKAAuK;IACvK,MAAM,CAAC,EAAE,AAAD,EAAG,AAAD,EAAG,UAAU,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC/D,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["function moduleNameFromPath(modulePath: string) {\n  if (modulePath.startsWith('@')) {\n    const [org, packageName] = modulePath.split('/');\n    if (org && packageName) {\n      return [org, packageName].join('/');\n    }\n    return modulePath;\n  }\n  const [packageName] = modulePath.split('/');\n  return packageName ? packageName : modulePath;\n}\n\nconst NODE_MODULE_PATTERN = /node_modules(\\/\\.(pnpm|store)\\/.*\\/node_modules)?\\//i;\n\nexport function getNodeModuleName(filePath: string): string | null {\n  // '/<project>/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp'\n  // '/<project>/node_modules/.pnpm/react-native@0.73.1_@babel+core@7.20.2_react@18.2.0/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp'\n  // '/<project>/node_modules/.store/react-native@0.73.1-OKL2xQk6utgOIuOl3VvO_g/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp'\n  const [, , , modulePath] = filePath.split(NODE_MODULE_PATTERN);\n  if (modulePath) {\n    return moduleNameFromPath(modulePath);\n  }\n  return null;\n}\n"]}