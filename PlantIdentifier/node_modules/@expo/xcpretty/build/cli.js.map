{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;;;;AACA,gDAAwB;AAExB,qCAA4D;AAE5D,MAAM,WAAW,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AAEzD,IAAA,yCAAgC,EAAC,WAAW,CAAC,CAAC", "sourcesContent": ["#!/usr/bin/env node\nimport path from 'path';\n\nimport { formatXcodeBuildPipeProcessAsync } from './Runner';\n\nconst projectRoot = path.resolve(process.argv[2] || '.');\n\nformatXcodeBuildPipeProcessAsync(projectRoot);\n"]}