import { type PanGestureHandlerProperties } from 'react-native-gesture-handler';
export declare function PanGestureHandler(props: PanGestureHandlerProperties): import("react/jsx-runtime").JSX.Element;
export type { PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
export { GestureHandlerRootView, State as GestureState, } from 'react-native-gesture-handler';
//# sourceMappingURL=GestureHandlerNative.d.ts.map