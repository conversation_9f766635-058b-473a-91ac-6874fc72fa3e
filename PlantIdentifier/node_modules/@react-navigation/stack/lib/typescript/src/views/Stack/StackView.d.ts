import { type LocaleDirection, type ParamListBase, type Route, type RouteProp, type StackNavigationState } from '@react-navigation/native';
import * as React from 'react';
import type { StackDescriptor, StackDescriptorMap, StackNavigationConfig, StackNavigationHelpers } from '../../types';
type Props = StackNavigationConfig & {
    direction: LocaleDirection;
    state: StackNavigationState<ParamListBase>;
    navigation: StackNavigationHelpers;
    descriptors: StackDescriptorMap;
    describe: (route: RouteProp<ParamListBase>, placeholder: boolean) => StackDescriptor;
};
type State = {
    routes: Route<string>[];
    previousRoutes: Route<string>[];
    previousDescriptors: StackDescriptorMap;
    openingRouteKeys: string[];
    closingRouteKeys: string[];
    replacingRouteKeys: string[];
    descriptors: StackDescriptorMap;
};
export declare class StackView extends React.Component<Props, State> {
    static getDerivedStateFromProps(props: Readonly<Props>, state: Readonly<State>): {
        routes: Route<string>[];
        previousRoutes: Route<string>[];
        descriptors: StackDescriptorMap;
        previousDescriptors: StackDescriptorMap;
        openingRouteKeys?: undefined;
        closingRouteKeys?: undefined;
        replacingRouteKeys?: undefined;
    } | {
        routes: import("@react-navigation/routers").NavigationRoute<ParamListBase, string>[];
        previousRoutes: import("@react-navigation/routers").NavigationRoute<ParamListBase, string>[];
        previousDescriptors: StackDescriptorMap;
        openingRouteKeys: string[];
        closingRouteKeys: string[];
        replacingRouteKeys: string[];
        descriptors: StackDescriptorMap;
    };
    state: State;
    private getPreviousRoute;
    private renderHeader;
    private handleOpenRoute;
    private handleCloseRoute;
    private handleTransitionStart;
    private handleTransitionEnd;
    private handleGestureStart;
    private handleGestureEnd;
    private handleGestureCancel;
    render(): import("react/jsx-runtime").JSX.Element;
}
export {};
//# sourceMappingURL=StackView.d.ts.map