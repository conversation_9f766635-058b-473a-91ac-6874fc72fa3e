{"version": 3, "names": ["React", "UIManager", "jsx", "_jsx", "RNCMaskedView", "require", "default", "e", "isMaskedViewAvailable", "getViewManagerConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "rest"], "sourceRoot": "../../src", "sources": ["MaskedViewNative.tsx"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AASzC,IAAIC,aAAyC;AAE7C,IAAI;EACF;EACA;EACAA,aAAa,GAAGC,OAAO,CAAC,uCAAuC,CAAC,CAACC,OAAO;AAC1E,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;AAAA;AAGF,MAAMC,qBAAqB,GACzBP,SAAS,CAACQ,oBAAoB,CAAC,eAAe,CAAC,IAAI,IAAI;AAEzD,OAAO,SAASC,UAAUA,CAAC;EAAEC,QAAQ;EAAE,GAAGC;AAAY,CAAC,EAAE;EACvD,IAAIJ,qBAAqB,IAAIJ,aAAa,EAAE;IAC1C,oBAAOD,IAAA,CAACC,aAAa;MAAA,GAAKQ,IAAI;MAAAD,QAAA,EAAGA;IAAQ,CAAgB,CAAC;EAC5D;EAEA,OAAOA,QAAQ;AACjB", "ignoreList": []}