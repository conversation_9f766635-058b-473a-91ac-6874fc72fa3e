import*as t from"../../core/i18n/i18n.js";import*as e from"../../third_party/lit/lit.js";export{Decorators,Directive,Directives,LitElement,noChange,nothing,render,svg}from"../../third_party/lit/lit.js";function n(t,...e){const n=[],r=[];let l="";for(let i=0;i<e.length;i++){const a=e[i];o(a)?(l+=t[i]+a.value,n.push(!1)):(l+=t[i],r.push(l),l="",n.push(!0))}return r.push(l+t[e.length]),r.raw=[...r],{strings:r,valueMap:n}}function r(t,...n){return n.some((t=>o(t)))?i(t,...n):e.html(t,...n)}function o(t){return"object"==typeof t&&null!==t&&"$$static$$"in t}const l=new WeakMap;function i(t,...r){const o=l.get(t);if(o){const t=r.filter(((t,e)=>!!o&&o.valueMap[e]));return e.html(o.strings,...t)}return l.set(t,n(t,...r)),i(t,...r)}var a=Object.freeze({__proto__:null,flattenTemplate:n,html:r,literal:function(t){return{value:t[0],$$static$$:!0}},i18nTemplate:function(e,n,o){const l=e.getLocalizedStringSetFor(t.DevToolsLocale.DevToolsLocale.instance().locale).getMessageFormatterFor(n);let i=r``;for(const t of l.getAst())if(1===t.type){const e=o[t.value];e&&(i=r`${i}${e}`)}else"value"in t&&(i=r`${i}${t.value}`);return i}});const{html:s,literal:c,flattenTemplate:u,i18nTemplate:p}=a;export{u as flattenTemplate,s as html,p as i18nTemplate,c as literal};
